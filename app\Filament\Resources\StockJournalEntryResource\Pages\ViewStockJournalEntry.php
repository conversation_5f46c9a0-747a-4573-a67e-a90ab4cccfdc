<?php

namespace App\Filament\Resources\StockJournalEntryResource\Pages;

use App\Filament\Resources\StockJournalEntryResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewStockJournalEntry extends ViewRecord
{
    protected static string $resource = StockJournalEntryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('print')
                ->label('طباعة')
                ->icon('heroicon-o-printer')
                ->action('printJournalEntry'),

            Actions\Action::make('view_stock_docs')
                ->label('عرض المستندات المخزنية')
                ->icon('heroicon-o-document')
                ->visible(fn () => $this->record->stockDocs->isNotEmpty())
                ->action('viewStockDocs'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('معلومات القيد المحاسبي')
                    ->schema([
                        Infolists\Components\Grid::make(3)
                            ->schema([
                                Infolists\Components\TextEntry::make('transaction_no')
                                    ->label('رقم القيد'),

                                Infolists\Components\TextEntry::make('date')
                                    ->label('التاريخ')
                                    ->date(),

                                Infolists\Components\TextEntry::make('reference')
                                    ->label('المرجع'),
                            ]),

                        Infolists\Components\TextEntry::make('narration')
                            ->label('البيان')
                            ->columnSpanFull(),

                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\IconEntry::make('posted')
                                    ->label('حالة الترحيل')
                                    ->boolean(),

                                Infolists\Components\TextEntry::make('created_at')
                                    ->label('تاريخ الإنشاء')
                                    ->dateTime(),
                            ]),
                    ]),

                Infolists\Components\Section::make('المستندات المخزنية المرتبطة')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('stockDocs')
                            ->schema([
                                Infolists\Components\Grid::make(4)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('id')
                                            ->label('رقم المستند'),

                                        Infolists\Components\TextEntry::make('transaction_type')
                                            ->label('نوع المعاملة')
                                            ->badge()
                                            ->color(fn (string $state): string => match ($state) {
                                                'Opening_Stock' => 'info',
                                                'Receipt' => 'success',
                                                'Delivery' => 'warning',
                                                'Adjustment' => 'danger',
                                                default => 'gray',
                                            }),

                                        Infolists\Components\TextEntry::make('location.name')
                                            ->label('الموقع'),

                                        Infolists\Components\TextEntry::make('transaction_date')
                                            ->label('تاريخ المعاملة')
                                            ->date(),
                                    ]),

                                Infolists\Components\TextEntry::make('description')
                                    ->label('الوصف')
                                    ->columnSpanFull(),
                            ])
                            ->columnSpanFull(),
                    ])
                    ->visible(fn () => $this->record->stockDocs->isNotEmpty()),

                Infolists\Components\Section::make('بنود القيد المحاسبي')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('lineItems')
                            ->schema([
                                Infolists\Components\Grid::make(5)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('account.name')
                                            ->label('الحساب'),

                                        Infolists\Components\TextEntry::make('account.code')
                                            ->label('كود الحساب'),

                                        Infolists\Components\TextEntry::make('narration')
                                            ->label('البيان'),

                                        Infolists\Components\TextEntry::make('amount')
                                            ->label('المبلغ')
                                            ->money(),

                                        Infolists\Components\TextEntry::make('credited')
                                            ->label('نوع البند')
                                            ->formatStateUsing(fn (bool $state): string => $state ? 'دائن' : 'مدين')
                                            ->badge()
                                            ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                                    ]),
                            ])
                            ->columnSpanFull(),

                        // ملخص القيد
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('total_debits')
                                    ->label('إجمالي المدين')
                                    ->state(fn () => $this->record->lineItems->where('credited', false)->sum('amount'))
                                    ->money()
                                    ->color('danger'),

                                Infolists\Components\TextEntry::make('total_credits')
                                    ->label('إجمالي الدائن')
                                    ->state(fn () => $this->record->lineItems->where('credited', true)->sum('amount'))
                                    ->money()
                                    ->color('success'),
                            ]),

                        Infolists\Components\TextEntry::make('balance_check')
                            ->label('توازن القيد')
                            ->state(function () {
                                $debits = $this->record->lineItems->where('credited', false)->sum('amount');
                                $credits = $this->record->lineItems->where('credited', true)->sum('amount');
                                $difference = abs($debits - $credits);
                                
                                if ($difference < 0.01) {
                                    return 'متوازن ✓';
                                } else {
                                    return "غير متوازن - الفرق: " . number_format($difference, 2);
                                }
                            })
                            ->badge()
                            ->color(function () {
                                $debits = $this->record->lineItems->where('credited', false)->sum('amount');
                                $credits = $this->record->lineItems->where('credited', true)->sum('amount');
                                return abs($debits - $credits) < 0.01 ? 'success' : 'danger';
                            }),
                    ]),
            ]);
    }

    public function printJournalEntry(): void
    {
        // منطق طباعة القيد
        // يمكن إعادة توجيه إلى صفحة طباعة أو تحميل PDF
    }

    public function viewStockDocs(): void
    {
        // منطق عرض المستندات المخزنية
        // يمكن فتح modal أو إعادة توجيه
    }
}
