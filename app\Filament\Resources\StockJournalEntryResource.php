<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\StockCluster;
use App\Models\JournalEntry;
use App\Models\Stocks\StockDoc;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class StockJournalEntryResource extends Resource
{
    protected static ?string $model = JournalEntry::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $cluster = StockCluster::class;

    protected static ?int $navigationSort = 30;

    public static function getNavigationLabel(): string
    {
        return 'القيود المحاسبية للمخازن';
    }

    public static function getPluralLabel(): string
    {
        return 'القيود المحاسبية للمخازن';
    }

    public static function getLabel(): string
    {
        return 'قيد محاسبي مخزني';
    }

    public static function getEloquentQuery(): Builder
    {
        // عرض القيود المحاسبية المرتبطة بالمستندات المخزنية فقط
        return parent::getEloquentQuery()
            ->whereHas('stockDocs')
            ->with(['lineItems.account', 'stockDocs.location']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات القيد')
                    ->schema([
                        Forms\Components\TextInput::make('transaction_no')
                            ->label('رقم القيد')
                            ->disabled(),

                        Forms\Components\DatePicker::make('date')
                            ->label('تاريخ القيد')
                            ->disabled(),

                        Forms\Components\Textarea::make('narration')
                            ->label('البيان')
                            ->disabled()
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('reference')
                            ->label('المرجع')
                            ->disabled(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('المستندات المخزنية المرتبطة')
                    ->schema([
                        Forms\Components\Repeater::make('stockDocs')
                            ->relationship()
                            ->schema([
                                Forms\Components\TextInput::make('id')
                                    ->label('رقم المستند')
                                    ->disabled(),

                                Forms\Components\TextInput::make('transaction_type')
                                    ->label('نوع المعاملة')
                                    ->disabled(),

                                Forms\Components\TextInput::make('location.name')
                                    ->label('الموقع')
                                    ->disabled(),

                                Forms\Components\DatePicker::make('transaction_date')
                                    ->label('تاريخ المعاملة')
                                    ->disabled(),
                            ])
                            ->columns(4)
                            ->disabled()
                            ->addable(false)
                            ->deletable(false),
                    ]),

                Forms\Components\Section::make('بنود القيد')
                    ->schema([
                        Forms\Components\Repeater::make('lineItems')
                            ->relationship()
                            ->schema([
                                Forms\Components\TextInput::make('account.name')
                                    ->label('الحساب')
                                    ->disabled(),

                                Forms\Components\TextInput::make('account.code')
                                    ->label('كود الحساب')
                                    ->disabled(),

                                Forms\Components\Textarea::make('narration')
                                    ->label('البيان')
                                    ->disabled(),

                                Forms\Components\TextInput::make('amount')
                                    ->label('المبلغ')
                                    ->disabled()
                                    ->numeric(),

                                Forms\Components\Select::make('credited')
                                    ->label('نوع البند')
                                    ->options([
                                        0 => 'مدين',
                                        1 => 'دائن',
                                    ])
                                    ->disabled(),
                            ])
                            ->columns(5)
                            ->disabled()
                            ->addable(false)
                            ->deletable(false),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('transaction_no')
                    ->label('رقم القيد')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('date')
                    ->label('التاريخ')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('narration')
                    ->label('البيان')
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\TextColumn::make('reference')
                    ->label('المرجع')
                    ->searchable(),

                Tables\Columns\TextColumn::make('stockDocs.transaction_type')
                    ->label('نوع المعاملة المخزنية')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Opening_Stock' => 'info',
                        'Receipt' => 'success',
                        'Delivery' => 'warning',
                        'Adjustment' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('stockDocs.location.name')
                    ->label('الموقع'),

                Tables\Columns\TextColumn::make('lineItems_sum_amount')
                    ->label('إجمالي المبلغ')
                    ->sum('lineItems', 'amount')
                    ->money()
                    ->sortable(),

                Tables\Columns\IconColumn::make('posted')
                    ->label('مرحل')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('stockDocs.transaction_type')
                    ->label('نوع المعاملة')
                    ->relationship('stockDocs', 'transaction_type')
                    ->options([
                        'Opening_Stock' => 'مخزون افتتاحي',
                        'Receipt' => 'استلام',
                        'Delivery' => 'تسليم',
                        'Adjustment' => 'تسوية',
                    ]),

                Tables\Filters\SelectFilter::make('stockDocs.location')
                    ->label('الموقع')
                    ->relationship('stockDocs.location', 'name'),

                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('to_date')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date', '>=', $date),
                            )
                            ->when(
                                $data['to_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date', '<=', $date),
                            );
                    }),

                Tables\Filters\TernaryFilter::make('posted')
                    ->label('حالة الترحيل'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('view_stock_doc')
                    ->label('عرض المستند المخزني')
                    ->icon('heroicon-o-document')
                    ->url(fn (JournalEntry $record): string => 
                        $record->stockDocs->first() 
                            ? route('filament.admin.resources.stock-docs.view', $record->stockDocs->first()) 
                            : '#'
                    )
                    ->visible(fn (JournalEntry $record): bool => $record->stockDocs->isNotEmpty()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\ExportBulkAction::make()
                        ->label('تصدير'),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Resources\StockJournalEntryResource\Pages\ListStockJournalEntries::route('/'),
            'view' => \App\Filament\Resources\StockJournalEntryResource\Pages\ViewStockJournalEntry::route('/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }
}


