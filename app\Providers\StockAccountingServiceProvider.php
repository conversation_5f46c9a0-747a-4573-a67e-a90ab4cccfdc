<?php

namespace App\Providers;

use App\Models\Stocks\StockDoc;
use App\Observers\StockDocObserver;
use App\Services\StockAccountingService;
use App\Services\StockMovementService;
use App\Services\StockReportingService;
use Illuminate\Support\ServiceProvider;

/**
 * مزود خدمة المحاسبة المخزنية
 */
class StockAccountingServiceProvider extends ServiceProvider
{
    /**
     * تسجيل الخدمات
     */
    public function register(): void
    {
        // تسجيل خدمة المحاسبة المخزنية كـ Singleton
        $this->app->singleton(StockAccountingService::class, function ($app) {
            return new StockAccountingService();
        });

        // تسجيل خدمة حركات المخزون كـ Singleton
        $this->app->singleton(StockMovementService::class, function ($app) {
            return new StockMovementService();
        });

        // تسجيل خدمة تقارير المخزون كـ Singleton
        $this->app->singleton(StockReportingService::class, function ($app) {
            return new StockReportingService();
        });
    }

    /**
     * تشغيل الخدمات
     */
    public function boot(): void
    {
        // تسجيل Observer للمستندات المخزنية
        StockDoc::observe(StockDocObserver::class);
    }
}
