<div>
    <h3 class="text-lg font-semibold mt-4">{{ $title }}</h3>

    @foreach ($accounts as $type => $data)
        <x-filament::section>
                <x-slot name="heading">{{config("ifrs.accounts.$type")}}</x-slot>

            <table class="w-full text-sm">
                <thead>
                    <tr>
                        <th class="py-2 pr-4 text-gray-700 dark:text-gray-300">الحساب</th>
                        <th class="py-2 text-right text-gray-900 dark:text-white font-medium">الرصيد</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($data['accounts'] as $account)
                        <tr>
                            <td>{{ $account['name'] }}</td>
                            <td class="text-right">
                                {{ number_format($account['closingBalance'][$this->entity->currency_id], 2) }}
                            </td>
                        </tr>
                    @endforeach
                    <tr class="font-bold">
                        <td>الاجمالي</td>
                        <td class="text-right">{{ number_format($data['balance'], 2) }}</td>
                    </tr>
                </tbody>
            </table>
        </x-filament::section>
    @endforeach

    <div class="mt-2 text-center text-sm">
        <strong>مدين: </strong>{{ number_format($summary['debit'], 2) }}, 
        <strong>دائن: </strong>{{ number_format($summary['credit'], 2) }}
    </div>
</div>
