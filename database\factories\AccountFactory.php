<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use IFRS\Models\Account as IfrsAccount;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Account>
 */
class AccountFactory extends Factory
{
    protected $model = Account::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' - حساب',
            'code' => $this->faker->unique()->numerify('####'),
            'account_type' => $this->faker->randomElement([
                IfrsAccount::INVENTORY,
                IfrsAccount::OPERATING_REVENUE,
                IfrsAccount::OPERATING_EXPENSE,
                IfrsAccount::BANK,
                IfrsAccount::RECEIVABLE,
                IfrsAccount::PAYABLE,
                IfrsAccount::EQUITY,
            ]),
            'currency_id' => 1, // افتراضي
            'entity_id' => 1, // افتراضي
        ];
    }

    /**
     * حساب مخزون
     */
    public function inventory(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::INVENTORY,
            'name' => 'حساب المخزون - ' . $this->faker->word,
        ]);
    }

    /**
     * حساب مبيعات
     */
    public function sales(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::OPERATING_REVENUE,
            'name' => 'حساب المبيعات - ' . $this->faker->word,
        ]);
    }

    /**
     * حساب مصروفات
     */
    public function expense(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::OPERATING_EXPENSE,
            'name' => 'حساب المصروفات - ' . $this->faker->word,
        ]);
    }

    /**
     * حساب نقدي/بنكي
     */
    public function cash(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::BANK,
            'name' => 'حساب النقدية - ' . $this->faker->word,
        ]);
    }

    /**
     * حساب عملاء
     */
    public function receivable(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::RECEIVABLE,
            'name' => 'حساب العملاء - ' . $this->faker->word,
        ]);
    }

    /**
     * حساب موردين
     */
    public function payable(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::PAYABLE,
            'name' => 'حساب الموردين - ' . $this->faker->word,
        ]);
    }

    /**
     * حساب حقوق ملكية
     */
    public function equity(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => IfrsAccount::EQUITY,
            'name' => 'حساب حقوق الملكية - ' . $this->faker->word,
        ]);
    }
}
