<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inv_items', function (Blueprint $table) {
            // حسابات محاسبية للصنف
            $table->unsignedBigInteger('inventory_account_id')->nullable()->after('tax_id');
            $table->unsignedBigInteger('sales_account_id')->nullable()->after('inventory_account_id');
            $table->unsignedBigInteger('cogs_account_id')->nullable()->after('sales_account_id');
            $table->unsignedBigInteger('purchase_account_id')->nullable()->after('cogs_account_id');
            
            // إعدادات محاسبية
            $table->boolean('track_inventory')->default(true)->after('purchase_account_id');
            $table->string('valuation_method')->default('fifo')->after('track_inventory'); // fifo, lifo, average
            $table->decimal('standard_cost', 10, 2)->nullable()->after('valuation_method');
            
            // Foreign keys
            $table->foreign('inventory_account_id')
                  ->references('id')
                  ->on(config('ifrs.table_prefix') . 'accounts')
                  ->onDelete('set null');
                  
            $table->foreign('sales_account_id')
                  ->references('id')
                  ->on(config('ifrs.table_prefix') . 'accounts')
                  ->onDelete('set null');
                  
            $table->foreign('cogs_account_id')
                  ->references('id')
                  ->on(config('ifrs.table_prefix') . 'accounts')
                  ->onDelete('set null');
                  
            $table->foreign('purchase_account_id')
                  ->references('id')
                  ->on(config('ifrs.table_prefix') . 'accounts')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inv_items', function (Blueprint $table) {
            $table->dropForeign(['inventory_account_id']);
            $table->dropForeign(['sales_account_id']);
            $table->dropForeign(['cogs_account_id']);
            $table->dropForeign(['purchase_account_id']);
            
            $table->dropColumn([
                'inventory_account_id',
                'sales_account_id', 
                'cogs_account_id',
                'purchase_account_id',
                'track_inventory',
                'valuation_method',
                'standard_cost'
            ]);
        });
    }
};
