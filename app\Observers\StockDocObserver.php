<?php

namespace App\Observers;

use App\Models\Stocks\StockDoc;
use App\Services\StockAccountingService;
use App\Services\StockMovementService;
use App\Settings\StockAccountingSettings;
use Illuminate\Support\Facades\Log;

/**
 * مراقب أحداث المستندات المخزنية
 * يتعامل مع إنشاء القيود المحاسبية التلقائية
 */
class StockDocObserver
{
    private StockAccountingService $stockAccountingService;
    private StockMovementService $stockMovementService;
    private StockAccountingSettings $settings;

    public function __construct()
    {
        $this->stockAccountingService = app(StockAccountingService::class);
        $this->stockMovementService = app(StockMovementService::class);
        $this->settings = app(StockAccountingSettings::class);
    }

    /**
     * معالجة حدث إنشاء مستند مخزني جديد
     */
    public function created(StockDoc $stockDoc): void
    {
        try {
            // معالجة المستند المخزني (تحديث الأرصدة + القيود المحاسبية)
            $this->stockMovementService->processStockDocument($stockDoc);

            Log::info('تم معالجة المستند المخزني بنجاح', [
                'stock_doc_id' => $stockDoc->id,
                'transaction_type' => $stockDoc->transaction_type
            ]);

        } catch (\Exception $e) {
            Log::error('فشل في معالجة المستند المخزني', [
                'stock_doc_id' => $stockDoc->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // يمكن إضافة إشعار للمستخدم هنا
            throw $e; // إعادة رمي الاستثناء لمنع حفظ المستند
        }
    }

    /**
     * معالجة حدث تحديث مستند مخزني
     */
    public function updated(StockDoc $stockDoc): void
    {
        // إذا تم تحديث المستند وكان له قيد محاسبي، قد نحتاج لتحديث القيد
        if ($stockDoc->journal_entry_id && $stockDoc->isDirty(['quantity', 'cost', 'price'])) {
            Log::info('تم تحديث مستند مخزني له قيد محاسبي', [
                'stock_doc_id' => $stockDoc->id,
                'journal_entry_id' => $stockDoc->journal_entry_id,
                'changes' => $stockDoc->getChanges()
            ]);
            
            // يمكن إضافة منطق لتحديث القيد المحاسبي هنا
        }
    }

    /**
     * معالجة حدث حذف مستند مخزني
     */
    public function deleting(StockDoc $stockDoc): void
    {
        // إذا كان للمستند قيد محاسبي، يجب التعامل معه
        if ($stockDoc->journal_entry_id) {
            Log::warning('محاولة حذف مستند مخزني له قيد محاسبي', [
                'stock_doc_id' => $stockDoc->id,
                'journal_entry_id' => $stockDoc->journal_entry_id
            ]);
            
            // يمكن منع الحذف أو إنشاء قيد عكسي
            // throw new \Exception('لا يمكن حذف مستند مخزني له قيد محاسبي مرحل');
        }
    }

    /**
     * معالجة حدث استعادة مستند مخزني محذوف
     */
    public function restored(StockDoc $stockDoc): void
    {
        Log::info('تم استعادة مستند مخزني', [
            'stock_doc_id' => $stockDoc->id,
            'journal_entry_id' => $stockDoc->journal_entry_id
        ]);
    }
}
