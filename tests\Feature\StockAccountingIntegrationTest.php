<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\StockAccountingService;
use App\Services\StockMovementService;
use App\Services\StockReportingService;
use App\Models\Stocks\StockDoc;
use App\Models\Stocks\StockDocItem;
use App\Models\Stocks\StockLocation;
use App\Models\Stocks\Stock;
use App\Models\Items\Item;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Settings\StockAccountingSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class StockAccountingIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private StockAccountingService $accountingService;
    private StockMovementService $movementService;
    private StockReportingService $reportingService;
    private StockAccountingSettings $settings;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->accountingService = app(StockAccountingService::class);
        $this->movementService = app(StockMovementService::class);
        $this->reportingService = app(StockReportingService::class);
        $this->settings = app(StockAccountingSettings::class);
        
        $this->setupTestEnvironment();
    }

    private function setupTestEnvironment(): void
    {
        // إنشاء الحسابات المطلوبة
        $inventoryAccount = Account::factory()->inventory()->create();
        $salesAccount = Account::factory()->sales()->create();
        $cogsAccount = Account::factory()->expense()->create();
        $cashAccount = Account::factory()->cash()->create();
        $adjustmentAccount = Account::factory()->expense()->create();
        $openingAccount = Account::factory()->equity()->create();

        // تحديث الإعدادات
        $this->settings->default_inventory_account = $inventoryAccount->id;
        $this->settings->sales_account = $salesAccount->id;
        $this->settings->cost_of_goods_sold_account = $cogsAccount->id;
        $this->settings->cash_account = $cashAccount->id;
        $this->settings->inventory_adjustment_account = $adjustmentAccount->id;
        $this->settings->opening_balance_account = $openingAccount->id;
        $this->settings->auto_create_journal_entries = true;
        $this->settings->create_cogs_entries = true;
        $this->settings->save();
    }

    /** @test */
    public function it_handles_complete_stock_lifecycle(): void
    {
        // إعداد البيانات الأساسية
        $location = StockLocation::factory()->create(['name' => 'المخزن الرئيسي']);
        $item = Item::factory()->create([
            'name' => 'منتج اختبار',
            'cost' => 100,
            'price' => 150,
            'sku_code' => 'TEST001'
        ]);

        // 1. المخزون الافتتاحي
        $openingDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $openingDoc->id,
            'item_id' => $item->id,
            'quantity' => 100,
            'cost' => 100,
        ]);

        // معالجة المخزون الافتتاحي
        $this->movementService->processStockDocument($openingDoc);

        // التحقق من الرصيد
        $balance = $this->movementService->getItemStockBalance($item->id, $location->id);
        $this->assertEquals(100, $balance);

        // التحقق من القيد المحاسبي
        $this->assertNotNull($openingDoc->fresh()->journal_entry_id);

        // 2. استلام بضاعة إضافية
        $receiptDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now()->subDays(20),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $receiptDoc->id,
            'item_id' => $item->id,
            'quantity' => 50,
            'cost' => 110, // تكلفة أعلى
        ]);

        $this->movementService->processStockDocument($receiptDoc);

        // التحقق من الرصيد الجديد
        $balance = $this->movementService->getItemStockBalance($item->id, $location->id);
        $this->assertEquals(150, $balance); // 100 + 50

        // 3. تسليم بضاعة (مبيعات)
        $deliveryDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::DELIVERY,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now()->subDays(10),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $deliveryDoc->id,
            'item_id' => $item->id,
            'quantity' => 30,
            'cost' => 100, // تكلفة الوحدة
            'price' => 150, // سعر البيع
        ]);

        $this->movementService->processStockDocument($deliveryDoc);

        // التحقق من الرصيد بعد البيع
        $balance = $this->movementService->getItemStockBalance($item->id, $location->id);
        $this->assertEquals(120, $balance); // 150 - 30

        // 4. تسوية مخزون
        $adjustmentDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::ADJUSTMENT,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now()->subDays(5),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $adjustmentDoc->id,
            'item_id' => $item->id,
            'quantity' => -5, // نقص في المخزون
            'cost' => 100,
        ]);

        $this->movementService->processStockDocument($adjustmentDoc);

        // التحقق من الرصيد النهائي
        $finalBalance = $this->movementService->getItemStockBalance($item->id, $location->id);
        $this->assertEquals(115, $finalBalance); // 120 - 5

        // 5. التحقق من التقارير
        
        // تقرير قيمة المخزون
        $valuationReport = $this->reportingService->getInventoryValuationReport();
        $this->assertEquals(1, $valuationReport['total_items']);
        $this->assertEquals(115, $valuationReport['total_quantity']);
        $this->assertEquals(11500, $valuationReport['total_value']); // 115 * 100

        // تقرير حركات المخزون
        $movementReport = $this->reportingService->getStockMovementReport(
            null, null,
            Carbon::now()->subDays(35),
            Carbon::now()
        );
        $this->assertEquals(4, $movementReport['total_movements']); // 4 مستندات

        // تقرير الربحية
        $profitabilityReport = $this->reportingService->getItemProfitabilityReport(
            Carbon::now()->subDays(35),
            Carbon::now()
        );
        $this->assertEquals(1, $profitabilityReport['summary']['total_items']);
        $this->assertEquals(4500, $profitabilityReport['summary']['total_sales_value']); // 30 * 150
        $this->assertEquals(3000, $profitabilityReport['summary']['total_cost_value']); // 30 * 100
        $this->assertEquals(1500, $profitabilityReport['summary']['total_profit']); // 4500 - 3000

        // تقرير القيود المحاسبية
        $accountingReport = $this->reportingService->getStockAccountingReport(
            Carbon::now()->subDays(35),
            Carbon::now()
        );
        $this->assertGreaterThanOrEqual(4, $accountingReport['summary']['total_entries']);

        // التحقق من توازن جميع القيود
        foreach ($accountingReport['entries'] as $entry) {
            $this->assertEquals($entry['total_debits'], $entry['total_credits']);
        }
    }

    /** @test */
    public function it_handles_multiple_locations_and_items(): void
    {
        // إعداد مواقع متعددة
        $location1 = StockLocation::factory()->create(['name' => 'مخزن 1']);
        $location2 = StockLocation::factory()->create(['name' => 'مخزن 2']);

        // إعداد أصناف متعددة
        $item1 = Item::factory()->create(['name' => 'صنف 1', 'cost' => 50]);
        $item2 = Item::factory()->create(['name' => 'صنف 2', 'cost' => 75]);

        // إنشاء مستندات متعددة
        $docs = [];
        
        // مخزون افتتاحي للموقع الأول
        $docs[] = $this->createStockDocument(StockDoc::OPENING, $location1, [
            ['item' => $item1, 'quantity' => 100, 'cost' => 50],
            ['item' => $item2, 'quantity' => 80, 'cost' => 75],
        ]);

        // مخزون افتتاحي للموقع الثاني
        $docs[] = $this->createStockDocument(StockDoc::OPENING, $location2, [
            ['item' => $item1, 'quantity' => 50, 'cost' => 50],
            ['item' => $item2, 'quantity' => 60, 'cost' => 75],
        ]);

        // معالجة جميع المستندات
        foreach ($docs as $doc) {
            $this->movementService->processStockDocument($doc);
        }

        // التحقق من الأرصدة
        $this->assertEquals(100, $this->movementService->getItemStockBalance($item1->id, $location1->id));
        $this->assertEquals(50, $this->movementService->getItemStockBalance($item1->id, $location2->id));
        $this->assertEquals(150, $this->movementService->getTotalItemStock($item1->id));

        // التحقق من تقرير قيمة المخزون
        $valuationReport = $this->reportingService->getInventoryValuationReport();
        $this->assertEquals(4, $valuationReport['total_items']); // 2 صنف × 2 موقع
        $this->assertEquals(290, $valuationReport['total_quantity']); // 100+80+50+60
        $this->assertEquals(19000, $valuationReport['total_value']); // (100+50)*50 + (80+60)*75

        // التحقق من تقرير موقع محدد
        $location1Report = $this->reportingService->getInventoryValuationReport($location1->id);
        $this->assertEquals(2, $location1Report['total_items']);
        $this->assertEquals(180, $location1Report['total_quantity']);
    }

    /** @test */
    public function it_handles_accounting_errors_gracefully(): void
    {
        // إزالة الحسابات المطلوبة لمحاكاة خطأ
        $this->settings->default_inventory_account = null;
        $this->settings->save();

        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 10,
            'cost' => 100,
        ]);

        // يجب أن يفشل بسبب عدم وجود الحسابات
        $this->expectException(\Exception::class);
        $this->movementService->processStockDocument($stockDoc);
    }

    private function createStockDocument(string $type, StockLocation $location, array $items): StockDoc
    {
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => $type,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now(),
        ]);

        foreach ($items as $itemData) {
            StockDocItem::factory()->create([
                'stock_doc_id' => $stockDoc->id,
                'item_id' => $itemData['item']->id,
                'quantity' => $itemData['quantity'],
                'cost' => $itemData['cost'],
                'price' => $itemData['price'] ?? null,
            ]);
        }

        return $stockDoc;
    }

    /** @test */
    public function it_maintains_data_consistency_across_services(): void
    {
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create(['cost' => 200]);

        // إنشاء مستند وتتبع التغييرات
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 25,
            'cost' => 200,
        ]);

        // معالجة المستند
        $this->movementService->processStockDocument($stockDoc);

        // التحقق من تطابق البيانات عبر الخدمات
        $movementBalance = $this->movementService->getItemStockBalance($item->id, $location->id);
        
        $valuationReport = $this->reportingService->getInventoryValuationReport($location->id);
        $reportBalance = $valuationReport['locations'][0]['items'][0]['quantity'];
        
        $this->assertEquals($movementBalance, $reportBalance);
        $this->assertEquals(25, $movementBalance);

        // التحقق من القيد المحاسبي
        $stockDoc->refresh();
        $this->assertNotNull($stockDoc->journal_entry_id);
        $this->assertTrue($stockDoc->accounting_posted);

        $journalEntry = $stockDoc->journalEntry;
        $totalDebits = $journalEntry->lineItems->where('credited', false)->sum('amount');
        $totalCredits = $journalEntry->lineItems->where('credited', true)->sum('amount');
        
        $this->assertEquals($totalDebits, $totalCredits);
        $this->assertEquals(5000, $totalDebits); // 25 * 200
    }
}
