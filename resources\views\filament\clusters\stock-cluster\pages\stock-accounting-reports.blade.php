<x-filament-panels::page>
    <div class="space-y-6">
        <!-- نموذج التصفية -->
        <div class="bg-white rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- عرض التقارير -->
        @if($this->activeTab === 0 && isset($this->getViewData()['valuationReport']))
            @php $report = $this->getViewData()['valuationReport']; @endphp
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تقرير قيمة المخزون</h3>
                    <p class="text-sm text-gray-500">كما في تاريخ: {{ $report['as_of_date'] }}</p>
                </div>
                
                <div class="p-6">
                    <!-- ملخص التقرير -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-blue-600">إجمالي الأصناف</div>
                            <div class="text-2xl font-bold text-blue-900">{{ number_format($report['total_items']) }}</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-green-600">إجمالي الكمية</div>
                            <div class="text-2xl font-bold text-green-900">{{ number_format($report['total_quantity']) }}</div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-purple-600">إجمالي القيمة</div>
                            <div class="text-2xl font-bold text-purple-900">{{ number_format($report['total_value'], 2) }}</div>
                        </div>
                    </div>

                    <!-- تفاصيل المواقع -->
                    @if(!empty($report['locations']))
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">تفاصيل المواقع</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموقع</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الأصناف</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي الكمية</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي القيمة</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($report['locations'] as $location)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $location['name'] }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($location['items_count']) }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($location['total_quantity']) }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($location['total_value'], 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif

                    <!-- تفاصيل الأصناف -->
                    @if(!empty($report['items']))
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-3">تفاصيل الأصناف</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموقع</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التكلفة</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي القيمة</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($report['items'] as $item)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['name'] }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['location'] }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['quantity']) }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['cost'], 2) }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['total_value'], 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        @if($this->activeTab === 1 && isset($this->getViewData()['movementsReport']))
            @php $report = $this->getViewData()['movementsReport']; @endphp
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تقرير حركات المخزون</h3>
                    <p class="text-sm text-gray-500">
                        من {{ $report['period']['from'] ?? 'البداية' }} إلى {{ $report['period']['to'] ?? 'النهاية' }}
                    </p>
                </div>
                
                <div class="p-6">
                    <div class="mb-4">
                        <span class="text-sm font-medium text-gray-600">إجمالي الحركات: </span>
                        <span class="text-lg font-bold text-gray-900">{{ number_format($report['total_movements']) }}</span>
                    </div>

                    @if(!empty($report['movements']))
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">نوع الحركة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموقع</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التكلفة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيد المحاسبي</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($report['movements'] as $movement)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $movement['date'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $movement['type'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $movement['item'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $movement['location'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($movement['quantity']) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($movement['cost'], 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($movement['accounting_posted'])
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        مرحل
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        غير مرحل
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        @if($this->activeTab === 2 && isset($this->getViewData()['accountingReport']))
            @php $report = $this->getViewData()['accountingReport']; @endphp
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تقرير القيود المحاسبية للمخزون</h3>
                    <p class="text-sm text-gray-500">
                        من {{ $report['period']['from'] ?? 'البداية' }} إلى {{ $report['period']['to'] ?? 'النهاية' }}
                    </p>
                </div>
                
                <div class="p-6">
                    <!-- ملخص القيود -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-blue-600">إجمالي القيود</div>
                            <div class="text-2xl font-bold text-blue-900">{{ number_format($report['summary']['total_entries']) }}</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-green-600">إجمالي المدين</div>
                            <div class="text-2xl font-bold text-green-900">{{ number_format($report['summary']['total_debits'], 2) }}</div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-red-600">إجمالي الدائن</div>
                            <div class="text-2xl font-bold text-red-900">{{ number_format($report['summary']['total_credits'], 2) }}</div>
                        </div>
                        <div class="bg-{{ $report['summary']['balance_check'] ? 'green' : 'red' }}-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-{{ $report['summary']['balance_check'] ? 'green' : 'red' }}-600">حالة التوازن</div>
                            <div class="text-lg font-bold text-{{ $report['summary']['balance_check'] ? 'green' : 'red' }}-900">
                                {{ $report['summary']['balance_check'] ? 'متوازن' : 'غير متوازن' }}
                            </div>
                        </div>
                    </div>

                    @if(!empty($report['entries']))
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم القيد</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">البيان</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">نوع المستند</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($report['entries'] as $entry)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $entry['date'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $entry['reference'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $entry['narration'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $entry['document_type'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($entry['amount'], 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        @if($this->activeTab === 3 && isset($this->getViewData()['profitabilityReport']))
            @php $report = $this->getViewData()['profitabilityReport']; @endphp
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تقرير ربحية الأصناف</h3>
                    <p class="text-sm text-gray-500">
                        من {{ $report['period']['from'] ?? 'البداية' }} إلى {{ $report['period']['to'] ?? 'النهاية' }}
                    </p>
                </div>
                
                <div class="p-6">
                    <!-- ملخص الربحية -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-blue-600">إجمالي المبيعات</div>
                            <div class="text-2xl font-bold text-blue-900">{{ number_format($report['summary']['total_sales'], 2) }}</div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-red-600">إجمالي التكلفة</div>
                            <div class="text-2xl font-bold text-red-900">{{ number_format($report['summary']['total_cost'], 2) }}</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-green-600">إجمالي الربح</div>
                            <div class="text-2xl font-bold text-green-900">{{ number_format($report['summary']['total_profit'], 2) }}</div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-purple-600">هامش الربح</div>
                            <div class="text-2xl font-bold text-purple-900">{{ number_format($report['summary']['profit_margin'], 1) }}%</div>
                        </div>
                    </div>

                    @if(!empty($report['items']))
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية المباعة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي المبيعات</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي التكلفة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الربح</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">هامش الربح</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($report['items'] as $item)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['name'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['quantity_sold']) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['total_sales'], 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['total_cost'], 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['profit'], 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['profit_margin'], 1) }}%</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        @if($this->activeTab === 4 && isset($this->getViewData()['slowMovingReport']))
            @php $report = $this->getViewData()['slowMovingReport']; @endphp
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تقرير الأصناف بطيئة الحركة</h3>
                    <p class="text-sm text-gray-500">الأصناف التي لم تتحرك خلال {{ $report['days_threshold'] }} يوم</p>
                </div>
                
                <div class="p-6">
                    <div class="mb-4">
                        <span class="text-sm font-medium text-gray-600">إجمالي الأصناف بطيئة الحركة: </span>
                        <span class="text-lg font-bold text-gray-900">{{ number_format($report['total_items']) }}</span>
                    </div>

                    @if(!empty($report['items']))
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموقع</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية الحالية</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر حركة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الأيام</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">قيمة المخزون</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($report['items'] as $item)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['name'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['location'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['current_quantity']) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['last_movement_date'] ?? 'لا توجد حركة' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['days_since_last_movement'] }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item['inventory_value'], 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
