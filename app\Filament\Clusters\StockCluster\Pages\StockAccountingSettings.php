<?php

namespace App\Filament\Clusters\StockCluster\Pages;

use App\Filament\Clusters\StockCluster;
use App\Settings\StockAccountingSettings as StockAccountingSettingsModel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use App\Models\Account;
use IFRS\Models\Account as IfrsAccount;
use Illuminate\Support\Facades\DB;

class StockAccountingSettings extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $settings = StockAccountingSettingsModel::class;

    protected static ?string $cluster = StockCluster::class;

    protected static ?int $navigationSort = 10;

    public static function getNavigationLabel(): string
    {
        return 'إعدادات المحاسبة المخزنية';
    }

    public function getTitle(): string
    {
        return 'إعدادات المحاسبة المخزنية';
    }

    public function mount(): void
    {
        // تهيئة الإعدادات إذا لم تكن موجودة
        $this->ensureSettingsExist();
        parent::mount();
    }

    protected function ensureSettingsExist(): void
    {
        $companyId = auth()->user()->company_id ?? 1;
        $group = 'stock_accounting_' . $companyId;

        $exists = DB::table('settings')
            ->where('group', $group)
            ->where('name', 'stock_accounting_settings')
            ->exists();

        if (!$exists) {
            $defaultSettings = [
                'default_inventory_account' => null,
                'inventory_adjustment_account' => null,
                'cost_of_goods_sold_account' => null,
                'sales_account' => null,
                'purchases_account' => null,
                'opening_balance_account' => null,
                'retained_earnings_account' => null,
                'cash_account' => null,
                'bank_account' => null,
                'default_customer_account' => null,
                'default_supplier_account' => null,
                'accounts_receivable_account' => null,
                'accounts_payable_account' => null,
                'auto_create_journal_entries' => true,
                'create_cogs_entries' => true,
                'separate_location_accounts' => false,
                'inventory_valuation_method' => 'fifo',
                'use_standard_cost' => false,
                'input_tax_account' => null,
                'output_tax_account' => null,
                'include_tax_in_inventory_cost' => true
            ];

            DB::table('settings')->insert([
                'group' => $group,
                'name' => 'stock_accounting_settings',
                'locked' => false,
                'payload' => json_encode($defaultSettings),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('الحسابات الأساسية')
                    ->description('تحديد الحسابات المحاسبية الأساسية للمخازن')
                    ->schema([
                        Forms\Components\Select::make('default_inventory_account')
                            ->label('حساب المخزون الافتراضي')
                            ->options($this->getInventoryAccounts())
                            ->searchable()
                            ->required()
                            ->helperText('الحساب الذي سيتم استخدامه لتسجيل قيمة المخزون'),

                        Forms\Components\Select::make('inventory_adjustment_account')
                            ->label('حساب تسوية المخزون')
                            ->options($this->getExpenseAccounts())
                            ->searchable()
                            ->required()
                            ->helperText('الحساب المستخدم لتسجيل تسويات المخزون'),

                        Forms\Components\Select::make('cost_of_goods_sold_account')
                            ->label('حساب تكلفة البضاعة المباعة')
                            ->options($this->getExpenseAccounts())
                            ->searchable()
                            ->required()
                            ->helperText('الحساب المستخدم لتسجيل تكلفة البضاعة المباعة'),

                        Forms\Components\Select::make('sales_account')
                            ->label('حساب المبيعات الافتراضي')
                            ->options($this->getRevenueAccounts())
                            ->searchable()
                            ->required()
                            ->helperText('الحساب الافتراضي لتسجيل المبيعات'),

                        Forms\Components\Select::make('purchases_account')
                            ->label('حساب المشتريات الافتراضي')
                            ->options($this->getExpenseAccounts())
                            ->searchable()
                            ->required()
                            ->helperText('الحساب الافتراضي لتسجيل المشتريات'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('حسابات الأرصدة الافتتاحية')
                    ->schema([
                        Forms\Components\Select::make('opening_balance_account')
                            ->label('حساب الأرصدة الافتتاحية')
                            ->options($this->getEquityAccounts())
                            ->searchable()
                            ->helperText('الحساب المستخدم للأرصدة الافتتاحية'),

                        Forms\Components\Select::make('retained_earnings_account')
                            ->label('حساب الأرباح المحتجزة')
                            ->options($this->getEquityAccounts())
                            ->searchable()
                            ->helperText('حساب الأرباح المحتجزة'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('الحسابات النقدية والبنكية')
                    ->schema([
                        Forms\Components\Select::make('cash_account')
                            ->label('حساب النقدية الافتراضي')
                            ->options($this->getCashAccounts())
                            ->searchable()
                            ->helperText('الحساب الافتراضي للنقدية'),

                        Forms\Components\Select::make('bank_account')
                            ->label('حساب البنك الافتراضي')
                            ->options($this->getBankAccounts())
                            ->searchable()
                            ->helperText('الحساب الافتراضي للبنك'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('حسابات العملاء والموردين')
                    ->schema([
                        Forms\Components\Select::make('default_customer_account')
                            ->label('حساب العملاء الافتراضي')
                            ->options($this->getReceivableAccounts())
                            ->searchable()
                            ->helperText('الحساب الافتراضي للعملاء'),

                        Forms\Components\Select::make('default_supplier_account')
                            ->label('حساب الموردين الافتراضي')
                            ->options($this->getPayableAccounts())
                            ->searchable()
                            ->helperText('الحساب الافتراضي للموردين'),

                        Forms\Components\Select::make('accounts_receivable_account')
                            ->label('حساب الذمم المدينة')
                            ->options($this->getReceivableAccounts())
                            ->searchable()
                            ->helperText('حساب الذمم المدينة العام'),

                        Forms\Components\Select::make('accounts_payable_account')
                            ->label('حساب الذمم الدائنة')
                            ->options($this->getPayableAccounts())
                            ->searchable()
                            ->helperText('حساب الذمم الدائنة العام'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('إعدادات السلوك')
                    ->schema([
                        Forms\Components\Toggle::make('auto_create_journal_entries')
                            ->label('إنشاء القيود المحاسبية تلقائياً')
                            ->default(true)
                            ->helperText('إنشاء قيود محاسبية تلقائياً عند حركات المخزون'),

                        Forms\Components\Toggle::make('create_cogs_entries')
                            ->label('إنشاء قيود تكلفة البضاعة المباعة')
                            ->default(true)
                            ->helperText('إنشاء قيود تكلفة البضاعة المباعة عند التسليم'),

                        Forms\Components\Toggle::make('separate_location_accounts')
                            ->label('حسابات منفصلة للمواقع')
                            ->default(false)
                            ->helperText('استخدام حسابات منفصلة لكل موقع مخزني'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('إعدادات التقييم')
                    ->schema([
                        Forms\Components\Select::make('inventory_valuation_method')
                            ->label('طريقة تقييم المخزون')
                            ->options([
                                'fifo' => 'الوارد أولاً صادر أولاً (FIFO)',
                                'lifo' => 'الوارد أخيراً صادر أولاً (LIFO)',
                                'average' => 'المتوسط المرجح',
                            ])
                            ->default('fifo')
                            ->required()
                            ->helperText('طريقة تقييم المخزون المستخدمة'),

                        Forms\Components\Toggle::make('use_standard_cost')
                            ->label('استخدام التكلفة المعيارية')
                            ->default(false)
                            ->helperText('استخدام التكلفة المعيارية بدلاً من التكلفة الفعلية'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('إعدادات الضرائب')
                    ->schema([
                        Forms\Components\Select::make('input_tax_account')
                            ->label('حساب ضريبة المدخلات')
                            ->options($this->getCurrentAssetAccounts())
                            ->searchable()
                            ->helperText('حساب ضريبة القيمة المضافة على المشتريات'),

                        Forms\Components\Select::make('output_tax_account')
                            ->label('حساب ضريبة المخرجات')
                            ->options($this->getCurrentLiabilityAccounts())
                            ->searchable()
                            ->helperText('حساب ضريبة القيمة المضافة على المبيعات'),

                        Forms\Components\Toggle::make('include_tax_in_inventory_cost')
                            ->label('تضمين الضريبة في تكلفة المخزون')
                            ->default(true)
                            ->helperText('تضمين ضريبة القيمة المضافة في تكلفة المخزون'),
                    ])
                    ->columns(3),
            ]);
    }

    // Helper methods for account options
    protected function getInventoryAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::INVENTORY
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getExpenseAccounts(): array
    {
        $expenseTypes = [
            IfrsAccount::OPERATING_EXPENSE,
            IfrsAccount::DIRECT_EXPENSE
        ];

        // إضافة الثوابت الإضافية إذا كانت موجودة (للتوافق مع إصدارات مختلفة من IFRS)
        try {
            $expenseTypes[] = IfrsAccount::NON_OPERATING_EXPENSE;
        } catch (\Error $e) {
            // الثابت غير موجود في هذا الإصدار من IFRS
        }

        try {
            $expenseTypes[] = IfrsAccount::OVERHEAD_EXPENSE;
        } catch (\Error $e) {
            // الثابت غير موجود في هذا الإصدار من IFRS
        }

        try {
            $expenseTypes[] = IfrsAccount::OTHER_EXPENSE;
        } catch (\Error $e) {
            // الثابت غير موجود في هذا الإصدار من IFRS
        }

        return Account::whereIn('account_type', array_unique($expenseTypes))
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getRevenueAccounts(): array
    {
        $revenueTypes = [
            IfrsAccount::OPERATING_REVENUE
        ];

        // إضافة الثوابت الإضافية إذا كانت موجودة (للتوافق مع إصدارات مختلفة من IFRS)
        try {
            $revenueTypes[] = IfrsAccount::NON_OPERATING_REVENUE;
        } catch (\Error $e) {
            // الثابت غير موجود في هذا الإصدار من IFRS
        }

        return Account::whereIn('account_type', array_unique($revenueTypes))
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getEquityAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::EQUITY
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getCashAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::BANK
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getBankAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::BANK
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getReceivableAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::RECEIVABLE
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getPayableAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::PAYABLE
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getCurrentAssetAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::CURRENT_ASSET
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }

    protected function getCurrentLiabilityAccounts(): array
    {
        return Account::whereIn('account_type', [
                         IfrsAccount::CURRENT_LIABILITY
                     ])
                     ->pluck('name', 'id')
                     ->toArray();
    }
}
