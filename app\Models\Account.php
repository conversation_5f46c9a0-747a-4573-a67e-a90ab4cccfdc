<?php

namespace App\Models;

use IFRS\Reports\AccountStatement;
use IFRS\Models\Account as IfAccount;
use Carbon\Carbon;
use IFRS\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Scopes\AccountSecurityLevelScope;
use App\Traits\DisableEntityScopeTrait;
use App\Traits\CompanySegregatingTrait;
use App\Traits\Owner\OwnerIdTrait;
use Illuminate\Support\Facades\Auth;
use IFRS\Scopes\EntityScope;

class Account extends IfAccount
{
    use DisableEntityScopeTrait;
    //use OwnerIdTrait;
    // use CompanySegregatingTrait;
    protected $fillable = [
        'name',
        'account_type',
        'account_id',
        'currency_id',
        'category_id',
        'entity_id',
        'description',
        'code',
        'security_level',
        'debit_limit',
        "company_id"
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class,'entity_id');
    }
    public function company()
    {
        return $this->belongsTo(Company::class,'company_id');
    }
    public static function boot(): void
    {
        parent::boot();
        static::addGlobalScope(new AccountSecurityLevelScope);
        static::addGlobalScope('enityAccount', function (Builder $builder) {
        //      dd(Auth::user(),Auth::user()->entity);
           // $builder->withoutGlobalScope(EntityScope::class);
            $user=Auth::user();
            if($user && $user->entity)
        //     if($user->entity->parent_id)
        //     $builder->whereIn('entity_id', [$user->entity->id,$user->company_id]);
        //    else
        //    {

            if($user->entity_id == $user->company_id)
                    {
                    //  $branches = $user->company->branches()->pluck('id')->toArray();
                    //  $branches[]=$user->entity_id;
                 //    dd($branches );
                     $builder->whereIn('entity_id',$user->entity->sibling_ids);
                    }
                    else
                    {
                     //  dd($user->entity->id,$user->company_id);
                        $builder->whereIn('entity_id', [$user->entity->id,$user->company_id]);
                    }

                    // $builder->where('entity_id', $user->entity_id);

        // }

        });
     }
     public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
    public static function openingBalances(int $year, $entity = null)
    {
        if (is_null($entity)) {
            $entity = Auth::user()->entity;
        }

        $accounts = collect([]);
        $balances = ['debit' => 0, 'credit' => 0];
        //foreach (Account::where('entity_id', '=', $entity->id)->get() as $account) {

        foreach (Account::whereIn('entity_id',$entity->sibling_ids )->get() as $account) {
            $account->openingBalance = $account->openingBalance($year, null, $entity)[$account->entity->currency_id];
            if ($account->openingBalance != 0) {
                $accounts->push($account);
            }
            if ($account->openingBalance > 0) {
                $balances['debit'] += $account->openingBalance;
            } else {
                $balances['credit'] += $account->openingBalance;
            }
        }
        return ['balances' => $balances, 'accounts' => $accounts];
    }

    /**
     * Chart of Account Section Balances for the Reporting Period.
     *
     * @param string $accountType
     * @param string | Carbon $startDate
     * @param string | Carbon $endDate
     * @param Entity|null $entity
     *
     * @return array
     */
    public static function sectionBalances(
        array $accountTypes,
        $startDate = null,
        $endDate = null,
        $fullBalance = true,
         $entity = null
    ): array
    {

        if (is_null($entity)) {
            $entity = Auth::user()->entity;
        }

        $balances = ['sectionOpeningBalance' => 0, 'sectionClosingBalance' => 0, 'sectionMovement' => 0, 'sectionCategories' => []];

        $startDate = is_null($startDate) ? ReportingPeriod::periodStart($endDate, $entity) : Carbon::parse($startDate);
        $endDate = is_null($endDate) ? Carbon::now() : Carbon::parse($endDate);
        $periodStart = ReportingPeriod::periodStart($endDate, $entity);

        $year = ReportingPeriod::year($endDate, $entity);
//  dd([$entity->id,Auth::user()->company_id,$entity->branch_ids,$entity->sibling_ids]);
        // foreach (Account::whereIn('account_type', $accountTypes)->where('company_id', '=', $entity->company_id)->get() as $account) {
                //foreach (Account::whereIn('account_type', $accountTypes)->where('entity_id', '=', $entity->id)->get() as $account) {
// dd($accountTypes);

//   dd( $startDate,$endDate,$year,$periodStart);
// dd(Account::whereIn('account_type', $accountTypes)->toSql());
      foreach (Account::whereIn('account_type', $accountTypes)->get() as $account) {
                //  print($account->name .' '.$account->id);

            $reportingCurrencyId = $account->entity->currency_id;
//   print($reportingCurrencyId );
            $account->openingBalance = $account->openingBalance($year)[$reportingCurrencyId] + $account->currentBalance($periodStart, $startDate)[$reportingCurrencyId];
            $account->balanceMovement = $account->currentBalance($startDate, $endDate)[$reportingCurrencyId];

            $account->closingBalance = $fullBalance ? $account->openingBalance + $account->balanceMovement : $account->balanceMovement;

            $account->balanceMovement *= -1;

            if ($account->closingBalance <> 0 || $account->balanceMovement <> 0) {

                if (is_null($account->category)) {
                    $categoryName = config('ifrs')['accounts'][$account->account_type];
                    $categoryId = 0;
                } else {
                    $category = $account->category;
                    $categoryName = $category->name;
                    $categoryId = $category->id;
                }

                if (array_key_exists($categoryName, $balances['sectionCategories'])) {
                    $balances['sectionCategories'][$categoryName]['accounts']->push((object)$account->attributes);
                    $balances['sectionCategories'][$categoryName]['total'] += $account->closingBalance;
                } else {
                    $balances['sectionCategories'][$categoryName]['accounts'] = collect([(object)$account->attributes]);
                    $balances['sectionCategories'][$categoryName]['total'] = $account->closingBalance;
                    $balances['sectionCategories'][$categoryName]['id'] = $categoryId;
                }
                $balances['sectionOpeningBalance'] += $account->openingBalance;
                $balances['sectionMovement'] += $account->balanceMovement;
                $balances['sectionClosingBalance'] += $account->closingBalance;
            }
        }

        return $balances;
    }
    public function balances()
    {
        return $this->hasMany(Balance::class);
    }

    /**
     * Account attributes.
     *
     * @return object
     */
    public function attributes()
    {
        $this->attributes['closingBalance'] = $this->closingBalance();
        // return (object)$this->attributes;
                return $this->attributes;

    }

    /**
     * Get Account's Closing Balances for the Reporting Period.
     *
     * @param string $endDate
     * @param int $currencyId
     *
     * @return array
     */
    public function closingBalance(string |null $endDate = null, int|null $currencyId = null): array
    {
        $entity = $this->entity;

        $endDate = is_null($endDate) ? ReportingPeriod::periodEnd(null, $entity) : Carbon::parse($endDate);
        $startDate = ReportingPeriod::periodStart($endDate, $entity);
        $year = ReportingPeriod::year($endDate, $entity);
        $balances = $this->openingBalance($year, $currencyId);
        $transactions = $this->currentBalance($startDate, $endDate, $currencyId);
        foreach (array_keys($balances) as $currency) {
            $balances[$currency] += $transactions[$currency];
        }
        return $balances;
    }

    /**
     * Get Account's Opening Balances for the Reporting Period.
     *
     * @param int $year
     * @param int $currencyId
     *
     * @return array
     */
    public function openingBalance(int|null $year = null, int|null $currencyId = null): array
    {
        $entity = $this->entity;

        $balances = [$entity->currency_id => 0];

        if (!is_null($year)) {
            $period = ReportingPeriod::getPeriod($year . "-01-01", $entity);
        } else {
            $period = $entity->current_reporting_period;
        }

        $openingBalances = $this->balances->whereIn('entity_id',$entity->sibling_ids )->filter(function ($balance, $key) use ($period) {
            return $balance->reporting_period_id == $period->id;
        });

        if (!is_null($currencyId)) {
            $openingBalances = $this->balances->whereIn('entity_id',$entity->sibling_ids )->filter(function ($balance, $key) use ($currencyId) {
                return $balance->currency_id == $currencyId;
            });
            $balances[$currencyId] = 0;
        }

        foreach ($openingBalances as $each) {

            if (!is_null($currencyId) && $entity->currency_id != $currencyId) {
                $each->balance_type == Balance::DEBIT ?
                    $balances[$currencyId] += $each->balance / $each->exchangeRate->rate :
                    $balances[$currencyId] -= $each->balance / $each->exchangeRate->rate;
            }
            $each->balance_type == Balance::DEBIT ?
                $balances[$entity->currency_id] += $each->balance :
                $balances[$entity->currency_id] -= $each->balance;
        }
        return $balances;
    }

    /**
     * Get Account's Current Balances for the Period given.
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int $currencyId
     *
     * @return array
     */
    public function currentBalance(Carbon |null $startDate = null, Carbon |null $endDate = null, int |null $currencyId = null): array
    {
        $entity = $this->entity;

        $startDate = is_null($startDate) ? ReportingPeriod::periodStart($endDate, $entity) : $startDate;
        $endDate = is_null($endDate) ?ReportingPeriod::periodEnd(null, $entity) : $endDate;
                // dd($startDate,$endDate,$currencyId);

        return Ledger::balance2($this, $startDate, $endDate, $currencyId);
    }

    /**
     * Check if the account has closing transactions.
     *
     * @param int $year
     *
     */
    public function isClosed(int|null $year = null): bool
    {
        if (is_null($year)) {
            $year = $this->entity->current_reporting_period->calendar_year;
        }
        return $this->closingTransactionsQuery($year)->count() > 0;
    }

    // // public function scopeBankAccount(Builder $query): Builder
    // // {
    // //     return $query->where('account_type', IfAccount::BANK);
    // // }


    // // public function getTrans()
    // // {

    // //     $report= AccountStatement($this->id);

    // //     dd($report->Transactions());
    // //     return $report->Transactions();

    // // }
    // public function transactionsReportQuery( $startDate,  $endDate, int $currencyId = null):Builder
    // {
    //     $transactionsTable = config('ifrs.table_prefix') . 'transactions';
    //     $ledgerTable = config('ifrs.table_prefix') . 'ledgers';
    //     // Transaction::leftJoin($ledgerTable, function($join) {
    //     //     $join->on($transactionsTable . 'ifrs_transactions.id', '=','ifrs_ledgers.transaction_id');
    //     //   })

    //     // $query = DB::table(
    //     //     $transactionsTable
    //     // )
    //     $query = (new Transaction)->newQuery()
    //     ->leftJoin($ledgerTable, function($join) {
    //                 $join->on( 'ifrs_transactions.id', '=','ifrs_ledgers.transaction_id');
    //               })
    //     // $query = Transaction::query()->leftJoin($ledgerTable, function($join) {
    //     //         $join->on( 'ifrs_transactions.id', '=','ifrs_ledgers.transaction_id');
    //     //       })


    //       //  ->leftJoin($ledgerTable, $transactionsTable . '.id', '=', $ledgerTable . '.transaction_id')
    //         ->where($transactionsTable . '.deleted_at', null)
    //         ->where($transactionsTable . '.entity_id', $this->entity_id)
    //         ->where($transactionsTable . '.transaction_date', '>=', $startDate)
    //         ->where($transactionsTable . '.transaction_date', '<=', Carbon::parse($endDate)->endOfDay())
    //         ->select(
    //             $transactionsTable . '.id',
    //             $transactionsTable . '.transaction_date',
    //             $transactionsTable . '.transaction_no',
    //             $transactionsTable . '.reference',
    //             $transactionsTable . '.transaction_type',
    //             $transactionsTable . '.credited',
    //             $transactionsTable . '.narration',
    //             $ledgerTable . '.rate',
    //             // $ledgerTable . '.amount',
    //             // $ledgerTable . '.entry_type',
    //             // $ledgerTable . '.post_account',



    //         )
    //        ->distinct()
    //        //->count($transactionsTable . '.id')
    //       // ->paginate(10)
    //         // ->paginate(10, ['*'], 'page', null, function ($query) {
    //         //     return $query->selectRaw('count(distinct ifrs_transactions.id) as aggregate');
    //         // })
    //         ;

    //     if (!is_null($currencyId)) {
    //         $query->where($transactionsTable . '.currency_id', $currencyId);
    //     }
    //     $query->where(
    //         function ($query) use ($ledgerTable) {
    //             $query->where($ledgerTable . '.post_account', $this->id)
    //                 ->orwhere($ledgerTable . '.folio_account', $this->id);
    //         }
    //     );

    //     return $query;
    // }
}
