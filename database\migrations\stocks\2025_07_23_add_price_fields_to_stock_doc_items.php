<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inv_stock_doc_items', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->nullable()->after('cost');
            $table->decimal('total_price', 10, 2)->nullable()->after('total_cost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inv_stock_doc_items', function (Blueprint $table) {
            $table->dropColumn(['price', 'total_price']);
        });
    }
};
