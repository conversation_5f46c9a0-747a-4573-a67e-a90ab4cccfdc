<?php

namespace App\Filament\Widgets;

use App\Models\Stocks\StockDoc;
use App\Models\Stocks\Stock;
use App\Models\JournalEntry;
use App\Services\StockReportingService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class StockAccountingStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    /**
     * الحصول على إجمالي قيمة المخزون
     */
    public function getTotalInventoryValue(): float
    {
        return $this->getTotalStockValue();
    }

    /**
     * الحصول على عدد حركات المخزون
     */
    public function getStockMovementsCount(): int
    {
        return StockDoc::whereMonth('transaction_date', Carbon::now()->month)
            ->whereYear('transaction_date', Carbon::now()->year)
            ->count();
    }

    /**
     * الحصول على عدد الأصناف بطيئة الحركة
     */
    public function getLowStockItemsCount(): int
    {
        $reportingService = app(StockReportingService::class);
        $slowMovingItems = $reportingService->getSlowMovingItemsReport(90);
        return $slowMovingItems['total_items'];
    }

    /**
     * الحصول على معدل دوران المخزون
     */
    public function getInventoryTurnoverRate(): string
    {
        // حساب معدل دوران المخزون
        $currentMonth = Carbon::now();

        // تكلفة البضاعة المباعة هذا الشهر
        $cogs = StockDoc::where('transaction_type', StockDoc::DELIVERY)
            ->whereMonth('transaction_date', $currentMonth->month)
            ->whereYear('transaction_date', $currentMonth->year)
            ->with('items.item')
            ->get()
            ->sum(function ($stockDoc) {
                return $stockDoc->items->sum(function ($item) {
                    return $item->quantity * ($item->item->cost ?? 0);
                });
            });

        // متوسط قيمة المخزون
        $averageInventory = $this->getTotalStockValue();

        if ($averageInventory > 0) {
            $turnoverRate = $cogs / $averageInventory;
            return number_format($turnoverRate, 2) . 'x';
        }

        return '0x';
    }

    public function getStats(): array
    {
        $reportingService = app(StockReportingService::class);
        
        // إحصائيات المخزون
        $totalStockValue = $this->getTotalStockValue();
        $totalStockItems = Stock::where('quantity', '>', 0)->count();
        
        // إحصائيات القيود المحاسبية
        $monthlyJournalEntries = JournalEntry::whereHas('stockDocs')
            ->whereMonth('transaction_date', Carbon::now()->month)
            ->whereYear('transaction_date', Carbon::now()->year)
            ->count();
            
        $monthlyStockDocs = StockDoc::whereMonth('transaction_date', Carbon::now()->month)
            ->whereYear('transaction_date', Carbon::now()->year)
            ->count();

        // إحصائيات الحركات
        $monthlyReceipts = StockDoc::where('transaction_type', StockDoc::RECEIPT)
            ->whereMonth('transaction_date', Carbon::now()->month)
            ->whereYear('transaction_date', Carbon::now()->year)
            ->count();

        $monthlyDeliveries = StockDoc::where('transaction_type', StockDoc::DELIVERY)
            ->whereMonth('transaction_date', Carbon::now()->month)
            ->whereYear('transaction_date', Carbon::now()->year)
            ->count();

        // الأصناف بطيئة الحركة
        $slowMovingItems = $reportingService->getSlowMovingItemsReport(90);
        $slowMovingCount = $slowMovingItems['total_items'];
        $slowMovingValue = $slowMovingItems['total_value_at_risk'];

        return [
            Stat::make('إجمالي قيمة المخزون', number_format($totalStockValue, 2))
                ->description('القيمة الإجمالية للمخزون الحالي')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success')
                ->chart($this->getStockValueChart()),

            Stat::make('عدد الأصناف المتوفرة', number_format($totalStockItems))
                ->description('الأصناف التي لديها رصيد متاح')
                ->descriptionIcon('heroicon-m-cube')
                ->color('info'),

            Stat::make('القيود المحاسبية هذا الشهر', number_format($monthlyJournalEntries))
                ->description('القيود المحاسبية للمستندات المخزنية')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('warning'),

            Stat::make('المستندات المخزنية هذا الشهر', number_format($monthlyStockDocs))
                ->description("{$monthlyReceipts} استلام، {$monthlyDeliveries} تسليم")
                ->descriptionIcon('heroicon-m-document-duplicate')
                ->color('primary'),

            Stat::make('الأصناف بطيئة الحركة', number_format($slowMovingCount))
                ->description('قيمة معرضة للخطر: ' . number_format($slowMovingValue, 2))
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($slowMovingCount > 0 ? 'danger' : 'success'),

            Stat::make('معدل دوران المخزون', $this->getInventoryTurnoverRate())
                ->description('معدل دوران المخزون الشهري')
                ->descriptionIcon('heroicon-m-arrow-path')
                ->color('info'),
        ];
    }

    private function getTotalStockValue(): float
    {
        return Stock::with('item')
            ->where('quantity', '>', 0)
            ->get()
            ->sum(function ($stock) {
                return $stock->quantity * $stock->item->cost;
            });
    }

    private function getStockValueChart(): array
    {
        // رسم بياني لقيمة المخزون خلال آخر 7 أيام
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            // هذا مثال بسيط - يمكن تحسينه لحساب القيمة الفعلية في كل تاريخ
            $value = $this->getStockValueAtDate($date);
            $data[] = $value;
        }
        return $data;
    }

    private function getStockValueAtDate(Carbon $date): float
    {
        // حساب مبسط - يمكن تحسينه
        return Stock::with('item')
            ->where('quantity', '>', 0)
            ->where('updated_at', '<=', $date)
            ->get()
            ->sum(function ($stock) {
                return $stock->quantity * ($stock->item->cost ?? 0);
            });
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
