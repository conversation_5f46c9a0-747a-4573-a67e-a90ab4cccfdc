<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inv_stock_docs', function (Blueprint $table) {
            $table->unsignedBigInteger('journal_entry_id')->nullable()->after('entity_id');
            $table->boolean('accounting_posted')->default(false)->after('journal_entry_id');
            
            $table->foreign('journal_entry_id')
                  ->references('id')
                  ->on(config('ifrs.table_prefix') . 'transactions')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inv_stock_docs', function (Blueprint $table) {
            $table->dropForeign(['journal_entry_id']);
            $table->dropColumn(['journal_entry_id', 'accounting_posted']);
        });
    }
};
