<?php

namespace App\Settings;

use Spatie\LaravelSettings\Settings;
use Illuminate\Support\Facades\Auth;

/**
 * إعدادات الحسابات المحاسبية للمخازن
 */
class StockAccountingSettings extends Settings
{
    // الحسابات الأساسية للمخزون
    public ?int $default_inventory_account = null;
    public ?int $inventory_adjustment_account = null;
    public ?int $cost_of_goods_sold_account = null;
    public ?int $sales_account = null;
    public ?int $purchases_account = null;
    
    // حسابات الأرصدة الافتتاحية
    public ?int $opening_balance_account = null;
    public ?int $retained_earnings_account = null;
    
    // الحسابات النقدية والبنكية
    public ?int $cash_account = null;
    public ?int $bank_account = null;
    
    // حسابات العملاء والموردين
    public ?int $default_customer_account = null;
    public ?int $default_supplier_account = null;
    public ?int $accounts_receivable_account = null;
    public ?int $accounts_payable_account = null;
    
    // إعدادات السلوك
    public bool $auto_create_journal_entries = true;
    public bool $create_cogs_entries = true;
    public bool $separate_location_accounts = false;
    
    // إعدادات التقييم
    public string $inventory_valuation_method = 'fifo'; // fifo, lifo, average
    public bool $use_standard_cost = false;
    
    // إعدادات الضرائب
    public ?int $input_tax_account = null;
    public ?int $output_tax_account = null;
    public bool $include_tax_in_inventory_cost = true;

    public static function group(): string
    {
        $companyId = Auth::check() ? Auth::user()->company_id : 1;
        return 'stock_accounting_' . $companyId;
    }

    /**
     * الحصول على حساب المخزون الافتراضي
     */
    public function getDefaultInventoryAccount(): ?int
    {
        return $this->default_inventory_account;
    }

    /**
     * الحصول على حساب تسوية المخزون
     */
    public function getInventoryAdjustmentAccount(): ?int
    {
        return $this->inventory_adjustment_account;
    }

    /**
     * الحصول على حساب تكلفة البضاعة المباعة
     */
    public function getCostOfGoodsSoldAccount(): ?int
    {
        return $this->cost_of_goods_sold_account;
    }

    /**
     * الحصول على حساب المبيعات
     */
    public function getSalesAccount(): ?int
    {
        return $this->sales_account;
    }

    /**
     * الحصول على حساب المشتريات
     */
    public function getPurchasesAccount(): ?int
    {
        return $this->purchases_account;
    }

    /**
     * الحصول على حساب الرصيد الافتتاحي
     */
    public function getOpeningBalanceAccount(): ?int
    {
        return $this->opening_balance_account ?? $this->retained_earnings_account;
    }

    /**
     * الحصول على الحساب النقدي
     */
    public function getCashAccount(): ?int
    {
        return $this->cash_account;
    }

    /**
     * الحصول على الحساب البنكي
     */
    public function getBankAccount(): ?int
    {
        return $this->bank_account;
    }

    /**
     * الحصول على حساب العملاء الافتراضي
     */
    public function getDefaultCustomerAccount(): ?int
    {
        return $this->default_customer_account ?? $this->accounts_receivable_account;
    }

    /**
     * الحصول على حساب الموردين الافتراضي
     */
    public function getDefaultSupplierAccount(): ?int
    {
        return $this->default_supplier_account ?? $this->accounts_payable_account;
    }

    /**
     * التحقق من تفعيل إنشاء القيود التلقائية
     */
    public function isAutoJournalEntriesEnabled(): bool
    {
        return $this->auto_create_journal_entries;
    }

    /**
     * التحقق من تفعيل قيود تكلفة البضاعة المباعة
     */
    public function isCogsEntriesEnabled(): bool
    {
        return $this->create_cogs_entries;
    }

    /**
     * التحقق من استخدام حسابات منفصلة للمواقع
     */
    public function useSeparateLocationAccounts(): bool
    {
        return $this->separate_location_accounts;
    }

    /**
     * الحصول على طريقة تقييم المخزون
     */
    public function getInventoryValuationMethod(): string
    {
        return $this->inventory_valuation_method;
    }

    /**
     * التحقق من استخدام التكلفة المعيارية
     */
    public function useStandardCost(): bool
    {
        return $this->use_standard_cost;
    }

    /**
     * الحصول على حساب ضريبة المدخلات
     */
    public function getInputTaxAccount(): ?int
    {
        return $this->input_tax_account;
    }

    /**
     * الحصول على حساب ضريبة المخرجات
     */
    public function getOutputTaxAccount(): ?int
    {
        return $this->output_tax_account;
    }

    /**
     * التحقق من تضمين الضريبة في تكلفة المخزون
     */
    public function includeTaxInInventoryCost(): bool
    {
        return $this->include_tax_in_inventory_cost;
    }

    /**
     * التحقق من اكتمال الإعدادات الأساسية
     */
    public function isConfigurationComplete(): bool
    {
        return !is_null($this->default_inventory_account) &&
               !is_null($this->cost_of_goods_sold_account) &&
               !is_null($this->sales_account) &&
               !is_null($this->cash_account);
    }

    /**
     * الحصول على قائمة بالحسابات المطلوبة المفقودة
     */
    public function getMissingRequiredAccounts(): array
    {
        $missing = [];
        
        if (is_null($this->default_inventory_account)) {
            $missing[] = 'حساب المخزون الافتراضي';
        }
        
        if (is_null($this->cost_of_goods_sold_account)) {
            $missing[] = 'حساب تكلفة البضاعة المباعة';
        }
        
        if (is_null($this->sales_account)) {
            $missing[] = 'حساب المبيعات';
        }
        
        if (is_null($this->cash_account)) {
            $missing[] = 'الحساب النقدي';
        }
        
        return $missing;
    }
}
