<?php

namespace App\Filament\Resources\StockJournalEntryResource\Pages;

use App\Filament\Resources\StockJournalEntryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListStockJournalEntries extends ListRecords
{
    protected static string $resource = StockJournalEntryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('export')
                ->label('تصدير التقرير')
                ->icon('heroicon-o-arrow-down-tray')
                ->action('exportReport'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('جميع القيود')
                ->badge(fn () => $this->getModel()::whereHas('stockDocs')->count()),

            'opening' => Tab::make('المخزون الافتتاحي')
                ->modifyQueryUsing(fn (Builder $query) => 
                    $query->whereHas('stockDocs', fn (Builder $q) => 
                        $q->where('transaction_type', 'Opening_Stock')
                    )
                )
                ->badge(fn () => $this->getModel()::whereHas('stockDocs', fn (Builder $q) => 
                    $q->where('transaction_type', 'Opening_Stock')
                )->count()),

            'receipts' => Tab::make('الاستلام')
                ->modifyQueryUsing(fn (Builder $query) => 
                    $query->whereHas('stockDocs', fn (Builder $q) => 
                        $q->where('transaction_type', 'Receipt')
                    )
                )
                ->badge(fn () => $this->getModel()::whereHas('stockDocs', fn (Builder $q) => 
                    $q->where('transaction_type', 'Receipt')
                )->count()),

            'deliveries' => Tab::make('التسليم')
                ->modifyQueryUsing(fn (Builder $query) => 
                    $query->whereHas('stockDocs', fn (Builder $q) => 
                        $q->where('transaction_type', 'Delivery')
                    )
                )
                ->badge(fn () => $this->getModel()::whereHas('stockDocs', fn (Builder $q) => 
                    $q->where('transaction_type', 'Delivery')
                )->count()),

            'adjustments' => Tab::make('التسويات')
                ->modifyQueryUsing(fn (Builder $query) => 
                    $query->whereHas('stockDocs', fn (Builder $q) => 
                        $q->where('transaction_type', 'Adjustment')
                    )
                )
                ->badge(fn () => $this->getModel()::whereHas('stockDocs', fn (Builder $q) => 
                    $q->where('transaction_type', 'Adjustment')
                )->count()),

            'posted' => Tab::make('المرحلة')
                ->modifyQueryUsing(fn (Builder $query) =>
                    $query->whereHas('ledgers')
                )
                ->badge(fn () => $this->getModel()::whereHas('stockDocs')
                    ->whereHas('ledgers')->count()),

            'unposted' => Tab::make('غير المرحلة')
                ->modifyQueryUsing(fn (Builder $query) =>
                    $query->whereDoesntHave('ledgers')
                )
                ->badge(fn () => $this->getModel()::whereHas('stockDocs')
                    ->whereDoesntHave('ledgers')->count()),
        ];
    }

    public function exportReport(): void
    {
        // منطق تصدير التقرير
        // يمكن استخدام Laravel Excel أو أي مكتبة أخرى
    }
}
