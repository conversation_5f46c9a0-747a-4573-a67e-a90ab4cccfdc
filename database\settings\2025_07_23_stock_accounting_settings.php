<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        // الحسابات الأساسية للمخزون
        $this->migrator->add('stock_accounting_1.default_inventory_account', null);
        $this->migrator->add('stock_accounting_1.inventory_adjustment_account', null);
        $this->migrator->add('stock_accounting_1.cost_of_goods_sold_account', null);
        $this->migrator->add('stock_accounting_1.sales_account', null);
        $this->migrator->add('stock_accounting_1.purchases_account', null);
        
        // حسابات الأرصدة الافتتاحية
        $this->migrator->add('stock_accounting_1.opening_balance_account', null);
        $this->migrator->add('stock_accounting_1.retained_earnings_account', null);
        
        // الحسابات النقدية والبنكية
        $this->migrator->add('stock_accounting_1.cash_account', null);
        $this->migrator->add('stock_accounting_1.bank_account', null);
        
        // حسابات العملاء والموردين
        $this->migrator->add('stock_accounting_1.default_customer_account', null);
        $this->migrator->add('stock_accounting_1.default_supplier_account', null);
        $this->migrator->add('stock_accounting_1.accounts_receivable_account', null);
        $this->migrator->add('stock_accounting_1.accounts_payable_account', null);
        
        // إعدادات السلوك
        $this->migrator->add('stock_accounting_1.auto_create_journal_entries', true);
        $this->migrator->add('stock_accounting_1.create_cogs_entries', true);
        $this->migrator->add('stock_accounting_1.separate_location_accounts', false);
        
        // إعدادات التقييم
        $this->migrator->add('stock_accounting_1.inventory_valuation_method', 'fifo');
        $this->migrator->add('stock_accounting_1.use_standard_cost', false);
        
        // إعدادات الضرائب
        $this->migrator->add('stock_accounting_1.input_tax_account', null);
        $this->migrator->add('stock_accounting_1.output_tax_account', null);
        $this->migrator->add('stock_accounting_1.include_tax_in_inventory_cost', true);
    }
};
