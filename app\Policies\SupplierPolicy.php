<?php

namespace App\Policies;


use App\Models\Parties\Supplier;
use App\Models\User;
use App\Traits\SystemPolicyTraitBefore;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class SupplierPolicy
{
       const USER_TYPE=1;
    use HandlesAuthorization;
    use SystemPolicyTraitBefore;
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_supplier');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Supplier $supplier): bool
    {
        return $user->can('view_supplier');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        if ($user->entity_id != $user->company_id )
        return $user->can('create_supplier');
        return false;
    }

    // /**
    //  * Determine whether the user can update the model.
    //  */
    // public function update(User $user, CustomerParty $customerParty): bool
    // {
    //     return false;
    // }

    // /**
    //  * Determine whether the user can delete the model.
    //  */
    // public function delete(User $user, CustomerParty $customerParty): bool
    // {
    //     return false;
    // }

    // /**
    //  * Determine whether the user can restore the model.
    //  */
    // public function restore(User $user, CustomerParty $customerParty): bool
    // {
    //     return false;
    // }

    // /**
    //  * Determine whether the user can permanently delete the model.
    //  */
    // public function forceDelete(User $user, CustomerParty $customerParty): bool
    // {
    //     return false;
    // }
}
