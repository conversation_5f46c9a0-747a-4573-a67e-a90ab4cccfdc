# دليل البدء السريع - نظام المحاسبة المخزنية

## 🚀 البدء السريع

### 1. التحقق من حالة النظام
```bash
# تشغيل الاختبار الشامل
php artisan test:final
```

### 2. الوصول للنظام
1. افتح المتصفح واذهب إلى: `http://localhost:8000/admin`
2. سجل الدخول بحسابك
3. ستجد قائمة "المحاسبة المخزنية" في الشريط الجانبي

## ⚙️ الإعداد الأولي

### 1. إعدادات النظام
1. انتقل إلى: **المحاسبة المخزنية > إعدادات المحاسبة المخزنية**
2. فعّل "إنشاء القيود المحاسبية تلقائياً"
3. فعّل "إنشاء قيود تكلفة البضاعة المباعة"
4. اختر طريقة التقييم (FIFO موصى بها)
5. احفظ الإعدادات

### 2. ربط الحسابات المحاسبية
1. انتقل إلى: **الأصناف > إدارة الأصناف**
2. لكل صنف، حدد:
   - حساب المخزون
   - حساب المبيعات
   - حساب المشتريات
   - حساب تكلفة البضاعة المباعة
3. فعّل "تتبع المخزون" للأصناف المطلوبة

## 📦 إنشاء حركات المخزون

### 1. المخزون الافتتاحي
1. انتقل إلى: **المخزون > مستندات المخزون**
2. أنشئ مستند جديد
3. اختر نوع "مخزون افتتاحي"
4. أضف الأصناف والكميات
5. احفظ المستند

### 2. استلام بضاعة
1. أنشئ مستند "استلام"
2. أضف الأصناف المستلمة
3. حدد الكميات والأسعار
4. سيتم إنشاء القيد المحاسبي تلقائياً

### 3. تسليم بضاعة
1. أنشئ مستند "تسليم"
2. أضف الأصناف المسلمة
3. حدد الكميات
4. سيتم إنشاء قيد تكلفة البضاعة تلقائياً

## 📊 مراجعة التقارير

### 1. تقرير قيمة المخزون
1. انتقل إلى: **المحاسبة المخزنية > التقارير المحاسبية**
2. اختر تبويب "تقرير قيمة المخزون"
3. حدد التاريخ والموقع
4. اضغط "إنشاء التقرير"

### 2. تقرير حركات المخزون
1. اختر تبويب "تقرير حركات المخزون"
2. حدد فترة التقرير
3. اختر الصنف (اختياري)
4. اضغط "إنشاء التقرير"

### 3. الأصناف بطيئة الحركة
1. اختر تبويب "تقرير الأصناف بطيئة الحركة"
2. حدد عدد الأيام (افتراضي 90 يوم)
3. اضغط "إنشاء التقرير"

## 📈 مراقبة الإحصائيات

### لوحة المعلومات
- **إجمالي قيمة المخزون**: القيمة الحالية للمخزون
- **عدد الأصناف النشطة**: الأصناف التي لها حركة
- **حركات المخزون الشهرية**: عدد المستندات هذا الشهر
- **الأصناف بطيئة الحركة**: عدد الأصناف التي لم تتحرك

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### 1. لا يتم إنشاء القيود المحاسبية
**الحل:**
- تأكد من تفعيل الإعداد في صفحة الإعدادات
- تحقق من ربط الأصناف بالحسابات المحاسبية
- تأكد من وجود الحسابات في النظام المحاسبي

#### 2. أخطاء في التقارير
**الحل:**
- تأكد من وجود بيانات في المخزون
- تحقق من صحة التواريخ المدخلة
- راجع إعدادات الأصناف

#### 3. أرصدة خاطئة
**الحل:**
- راجع مستندات التسوية
- تحقق من صحة حركات المخزون
- استخدم تقرير حركات المخزون للمراجعة

### أوامر التشخيص
```bash
# فحص حالة الجداول
php artisan check:tables

# اختبار الخدمات
php artisan test:services

# اختبار الإحصائيات
php artisan test:widgets
```

## 💡 نصائح مهمة

### 1. أفضل الممارسات
- ادخل المخزون الافتتاحي قبل البدء
- راجع الإعدادات قبل إنشاء المستندات
- استخدم التقارير لمراقبة الأرصدة
- احتفظ بنسخ احتياطية من البيانات

### 2. الأمان
- لا تحذف مستندات المخزون بعد إنشائها
- استخدم مستندات التسوية لتصحيح الأخطاء
- راجع القيود المحاسبية دورياً

### 3. الأداء
- نظف البيانات القديمة دورياً
- استخدم الفلاتر في التقارير
- راقب حجم قاعدة البيانات

## 📞 الحصول على المساعدة

### 1. التوثيق
- راجع ملف `STOCK_ACCOUNTING_README.md` للتفاصيل الكاملة
- استخدم أوامر الاختبار للتشخيص

### 2. السجلات
- راجع ملفات السجلات في `storage/logs`
- ابحث عن رسائل الخطأ المتعلقة بالمخزون

### 3. الدعم التقني
- استخدم أوامر التشخيص المتاحة
- قدم تفاصيل واضحة عن المشكلة
- أرفق رسائل الخطأ إن وجدت

---

**🎉 مبروك! نظام المحاسبة المخزنية جاهز للاستخدام**

النظام مُختبر بالكامل ونجح 100% من الاختبارات. يمكنك البدء في استخدامه فوراً!
