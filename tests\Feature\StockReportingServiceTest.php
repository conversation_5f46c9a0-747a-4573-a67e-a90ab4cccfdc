<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\StockReportingService;
use App\Models\Stocks\StockDoc;
use App\Models\Stocks\StockDocItem;
use App\Models\Stocks\StockLocation;
use App\Models\Stocks\Stock;
use App\Models\Items\Item;
use App\Models\JournalEntry;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class StockReportingServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private StockReportingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(StockReportingService::class);
    }

    /** @test */
    public function it_generates_inventory_valuation_report(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create(['name' => 'المخزن الرئيسي']);
        $item1 = Item::factory()->create(['name' => 'صنف 1', 'cost' => 100, 'sku_code' => 'SKU001']);
        $item2 = Item::factory()->create(['name' => 'صنف 2', 'cost' => 200, 'sku_code' => 'SKU002']);
        
        Stock::factory()->create([
            'item_id' => $item1->id,
            'location_id' => $location->id,
            'quantity' => 10,
        ]);

        Stock::factory()->create([
            'item_id' => $item2->id,
            'location_id' => $location->id,
            'quantity' => 5,
        ]);

        // تنفيذ الاختبار
        $report = $this->service->getInventoryValuationReport();

        // التحقق من النتائج
        $this->assertIsArray($report);
        $this->assertArrayHasKey('total_items', $report);
        $this->assertArrayHasKey('total_quantity', $report);
        $this->assertArrayHasKey('total_value', $report);
        $this->assertArrayHasKey('locations', $report);

        $this->assertEquals(2, $report['total_items']);
        $this->assertEquals(15, $report['total_quantity']); // 10 + 5
        $this->assertEquals(2000, $report['total_value']); // (10*100) + (5*200)

        // التحقق من تفاصيل الموقع
        $this->assertCount(1, $report['locations']);
        $locationData = $report['locations'][0];
        $this->assertEquals('المخزن الرئيسي', $locationData['location_name']);
        $this->assertEquals(2, $locationData['items_count']);
        $this->assertCount(2, $locationData['items']);
    }

    /** @test */
    public function it_generates_stock_movement_report(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create(['name' => 'المخزن الرئيسي']);
        $item = Item::factory()->create(['name' => 'صنف اختبار', 'cost' => 50, 'sku_code' => 'TEST001']);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now(),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 20,
            'cost' => 50,
            'price' => 75,
        ]);

        // تنفيذ الاختبار
        $report = $this->service->getStockMovementReport(
            null, // جميع الأصناف
            null, // جميع المواقع
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        // التحقق من النتائج
        $this->assertIsArray($report);
        $this->assertArrayHasKey('total_movements', $report);
        $this->assertArrayHasKey('movements', $report);
        $this->assertArrayHasKey('period', $report);

        $this->assertEquals(1, $report['total_movements']);
        $this->assertCount(1, $report['movements']);

        $movement = $report['movements'][0];
        $this->assertEquals($stockDoc->id, $movement['document_id']);
        $this->assertEquals(StockDoc::RECEIPT, $movement['transaction_type']);
        $this->assertEquals('المخزن الرئيسي', $movement['location_name']);
        $this->assertEquals('صنف اختبار', $movement['item_name']);
        $this->assertEquals(20, $movement['quantity']);
        $this->assertEquals(1000, $movement['total_cost']); // 20 * 50
        $this->assertEquals(1500, $movement['total_price']); // 20 * 75
    }

    /** @test */
    public function it_filters_movement_report_by_item(): void
    {
        $location = StockLocation::factory()->create();
        $item1 = Item::factory()->create(['name' => 'صنف 1']);
        $item2 = Item::factory()->create(['name' => 'صنف 2']);
        
        // إنشاء مستندات لصنفين مختلفين
        $stockDoc1 = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now(),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc1->id,
            'item_id' => $item1->id,
            'quantity' => 10,
            'cost' => 100,
        ]);

        $stockDoc2 = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now(),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc2->id,
            'item_id' => $item2->id,
            'quantity' => 5,
            'cost' => 200,
        ]);

        // تنفيذ الاختبار - تصفية بالصنف الأول فقط
        $report = $this->service->getStockMovementReport(
            $item1->id,
            null,
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        // التحقق من النتائج
        $this->assertEquals(1, $report['total_movements']);
        $this->assertEquals('صنف 1', $report['movements'][0]['item_name']);
    }

    /** @test */
    public function it_generates_stock_accounting_report(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create(['name' => 'المخزن الرئيسي']);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now(),
        ]);

        // إنشاء قيد محاسبي مرتبط
        $journalEntry = JournalEntry::factory()->create([
            'date' => Carbon::now(),
            'narration' => 'استلام بضاعة',
            'reference' => 'STOCK-RECEIPT-' . $stockDoc->id,
        ]);

        $stockDoc->update(['journal_entry_id' => $journalEntry->id]);

        // تنفيذ الاختبار
        $report = $this->service->getStockAccountingReport(
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        // التحقق من النتائج
        $this->assertIsArray($report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('entries', $report);

        $this->assertEquals(1, $report['summary']['total_entries']);
        $this->assertCount(1, $report['entries']);

        $entry = $report['entries'][0];
        $this->assertEquals($stockDoc->id, $entry['stock_doc_id']);
        $this->assertEquals($journalEntry->id, $entry['journal_entry_id']);
        $this->assertEquals(StockDoc::RECEIPT, $entry['transaction_type']);
        $this->assertEquals('المخزن الرئيسي', $entry['location_name']);
    }

    /** @test */
    public function it_generates_item_profitability_report(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create([
            'name' => 'صنف مربح',
            'cost' => 100,
            'sku_code' => 'PROFIT001'
        ]);
        
        // إنشاء مستند تسليم (مبيعات)
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::DELIVERY,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now(),
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 10,
            'cost' => 100,
            'price' => 150, // ربح 50 لكل وحدة
        ]);

        // تنفيذ الاختبار
        $report = $this->service->getItemProfitabilityReport(
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        // التحقق من النتائج
        $this->assertIsArray($report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('items', $report);

        $summary = $report['summary'];
        $this->assertEquals(1, $summary['total_items']);
        $this->assertEquals(1500, $summary['total_sales_value']); // 10 * 150
        $this->assertEquals(1000, $summary['total_cost_value']); // 10 * 100
        $this->assertEquals(500, $summary['total_profit']); // 1500 - 1000
        $this->assertEquals(33.33, round($summary['overall_profit_margin'], 2)); // (500/1500)*100

        $this->assertCount(1, $report['items']);
        $itemData = $report['items'][0];
        $this->assertEquals('صنف مربح', $itemData['item_name']);
        $this->assertEquals(10, $itemData['total_quantity_sold']);
        $this->assertEquals(500, $itemData['total_profit']);
    }

    /** @test */
    public function it_generates_slow_moving_items_report(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create(['name' => 'المخزن الرئيسي']);
        $slowItem = Item::factory()->create([
            'name' => 'صنف بطيء الحركة',
            'cost' => 200,
            'sku_code' => 'SLOW001'
        ]);

        $fastItem = Item::factory()->create([
            'name' => 'صنف سريع الحركة',
            'cost' => 100,
            'sku_code' => 'FAST001'
        ]);
        
        // إنشاء أرصدة
        Stock::factory()->create([
            'item_id' => $slowItem->id,
            'location_id' => $location->id,
            'quantity' => 50,
        ]);

        Stock::factory()->create([
            'item_id' => $fastItem->id,
            'location_id' => $location->id,
            'quantity' => 30,
        ]);

        // إنشاء مبيعات حديثة للصنف السريع فقط
        $recentSale = StockDoc::factory()->create([
            'transaction_type' => StockDoc::DELIVERY,
            'location_id' => $location->id,
            'transaction_date' => Carbon::now()->subDays(10), // خلال آخر 90 يوم
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $recentSale->id,
            'item_id' => $fastItem->id,
            'quantity' => 5,
            'cost' => 100,
            'price' => 150,
        ]);

        // تنفيذ الاختبار
        $report = $this->service->getSlowMovingItemsReport(90);

        // التحقق من النتائج
        $this->assertIsArray($report);
        $this->assertArrayHasKey('total_items', $report);
        $this->assertArrayHasKey('total_value_at_risk', $report);
        $this->assertArrayHasKey('items', $report);

        $this->assertEquals(1, $report['total_items']); // الصنف البطيء فقط
        $this->assertEquals(10000, $report['total_value_at_risk']); // 50 * 200

        $this->assertCount(1, $report['items']);
        $slowItemData = $report['items'][0];
        $this->assertEquals('صنف بطيء الحركة', $slowItemData['item_name']);
        $this->assertEquals(50, $slowItemData['quantity']);
        $this->assertEquals(10000, $slowItemData['total_value']);
    }

    /** @test */
    public function it_filters_valuation_report_by_location(): void
    {
        $location1 = StockLocation::factory()->create(['name' => 'مخزن 1']);
        $location2 = StockLocation::factory()->create(['name' => 'مخزن 2']);
        $item = Item::factory()->create(['cost' => 100]);
        
        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location1->id,
            'quantity' => 10,
        ]);

        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location2->id,
            'quantity' => 20,
        ]);

        // تنفيذ الاختبار - تصفية بالموقع الأول فقط
        $report = $this->service->getInventoryValuationReport($location1->id);

        // التحقق من النتائج
        $this->assertEquals(1, $report['total_items']);
        $this->assertEquals(10, $report['total_quantity']);
        $this->assertEquals(1000, $report['total_value']);
        $this->assertCount(1, $report['locations']);
        $this->assertEquals('مخزن 1', $report['locations'][0]['location_name']);
    }

    /** @test */
    public function it_handles_empty_stock_gracefully(): void
    {
        // تنفيذ الاختبار بدون أي أرصدة
        $report = $this->service->getInventoryValuationReport();

        // التحقق من النتائج
        $this->assertEquals(0, $report['total_items']);
        $this->assertEquals(0, $report['total_quantity']);
        $this->assertEquals(0, $report['total_value']);
        $this->assertCount(0, $report['locations']);
    }
}
