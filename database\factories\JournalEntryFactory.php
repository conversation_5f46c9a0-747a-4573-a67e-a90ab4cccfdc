<?php

namespace Database\Factories;

use App\Models\JournalEntry;
use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JournalEntry>
 */
class JournalEntryFactory extends Factory
{
    protected $model = JournalEntry::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'narration' => $this->faker->sentence(),
            'reference' => 'JE-' . $this->faker->unique()->numerify('######'),
            'currency_id' => 1, // افتراضي
            'entity_id' => 1, // افتراضي
            'posted' => $this->faker->boolean(80), // 80% مرحلة
        ];
    }

    /**
     * قيد مرحل
     */
    public function posted(): static
    {
        return $this->state(fn (array $attributes) => [
            'posted' => true,
        ]);
    }

    /**
     * قيد غير مرحل
     */
    public function unposted(): static
    {
        return $this->state(fn (array $attributes) => [
            'posted' => false,
        ]);
    }

    /**
     * قيد لهذا الشهر
     */
    public function thisMonth(): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => $this->faker->dateTimeBetween(
                Carbon::now()->startOfMonth(),
                Carbon::now()->endOfMonth()
            ),
        ]);
    }

    /**
     * قيد مخزني
     */
    public function stockRelated(): static
    {
        return $this->state(fn (array $attributes) => [
            'narration' => $this->faker->randomElement([
                'مخزون افتتاحي',
                'استلام بضاعة',
                'تسليم بضاعة',
                'تسوية مخزون',
                'تكلفة البضاعة المباعة'
            ]) . ' - ' . $this->faker->word,
            'reference' => 'STOCK-' . $this->faker->randomElement(['OPENING', 'RECEIPT', 'DELIVERY', 'ADJUSTMENT']) . '-' . $this->faker->numerify('####'),
        ]);
    }
}
