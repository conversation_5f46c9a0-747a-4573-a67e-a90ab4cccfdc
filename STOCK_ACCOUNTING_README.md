# نظام المحاسبة المخزنية المحدث

## نظرة عامة

تم تطوير نظام محاسبة مخزنية شامل يربط بين حركات المخزون والقيود المحاسبية بشكل تلقائي، مما يضمن دقة البيانات المالية وسهولة المتابعة.

## الميزات الجديدة

### 1. الربط المحاسبي التلقائي
- إنشاء قيود محاسبية تلقائية لجميع حركات المخزون
- دعم أنواع المستندات: المخزون الافتتاحي، الاستلام، التسليم، التسوية
- قيود منفصلة لتكلفة البضاعة المباعة
- توازن تلقائي للقيود المحاسبية

### 2. إعدادات محاسبية مرنة
- ربط الحسابات المحاسبية بالأصناف والمواقع
- طرق تقييم متعددة (FIFO, LIFO, المتوسط المرجح)
- إعدادات الضرائب والحسابات الافتراضية
- تحكم في السلوك التلقائي للنظام

### 3. تقارير محاسبية شاملة
- تقرير قيمة المخزون
- تقرير حركات المخزون مع القيود المحاسبية
- تقرير ربحية الأصناف
- تقرير الأصناف بطيئة الحركة
- تقرير القيود المحاسبية للمخازن

### 4. واجهات إدارية محسنة
- صفحة إعدادات المحاسبة المخزنية
- واجهات تقارير تفاعلية
- عرض القيود المحاسبية المرتبطة بالمستندات
- إحصائيات ورسوم بيانية

## البنية التقنية

### الخدمات الأساسية

#### StockAccountingService
```php
// إنشاء قيد محاسبي لمستند مخزني
$journalEntry = $stockAccountingService->createStockJournalEntry($stockDoc);
```

#### StockMovementService
```php
// معالجة مستند مخزني (تحديث الأرصدة + القيود المحاسبية)
$result = $stockMovementService->processStockDocument($stockDoc);
```

#### StockReportingService
```php
// تقرير قيمة المخزون
$report = $stockReportingService->getInventoryValuationReport();

// تقرير حركات المخزون
$report = $stockReportingService->getStockMovementReport($itemId, $locationId, $fromDate, $toDate);
```

### النماذج المحدثة

#### Item (الأصناف)
- `inventory_account_id`: حساب المخزون للصنف
- `sales_account_id`: حساب المبيعات للصنف
- `cogs_account_id`: حساب تكلفة البضاعة المباعة
- `purchase_account_id`: حساب المشتريات للصنف
- `track_inventory`: تفعيل تتبع المخزون
- `valuation_method`: طريقة التقييم
- `standard_cost`: التكلفة المعيارية

#### StockDoc (المستندات المخزنية)
- `journal_entry_id`: ربط بالقيد المحاسبي
- `accounting_posted`: حالة الترحيل المحاسبي

#### StockDocItem (بنود المستندات)
- `price`: سعر البيع للوحدة
- `total_price`: إجمالي قيمة المبيعات

### الإعدادات

#### StockAccountingSettings
```php
// الحسابات الأساسية
$settings->default_inventory_account = $accountId;
$settings->sales_account = $accountId;
$settings->cost_of_goods_sold_account = $accountId;

// السلوك التلقائي
$settings->auto_create_journal_entries = true;
$settings->create_cogs_entries = true;

// طريقة التقييم
$settings->inventory_valuation_method = 'fifo';
```

## التثبيت والإعداد

### 1. تشغيل الـ Migrations
```bash
php artisan migrate
```

### 2. تسجيل Service Provider
تم إضافة `StockAccountingServiceProvider` في `bootstrap/providers.php`

### 3. إعداد الحسابات المحاسبية
1. انتقل إلى "إعدادات المخازن" > "إعدادات المحاسبة المخزنية"
2. حدد الحسابات المحاسبية المطلوبة
3. اختر إعدادات السلوك والتقييم

### 4. ربط الأصناف بالحسابات (اختياري)
في صفحة تحرير الصنف، يمكن تحديد حسابات محاسبية خاصة بكل صنف

## الاستخدام

### إنشاء مستند مخزني
عند إنشاء أي مستند مخزني، سيتم تلقائياً:
1. تحديث أرصدة المخزون
2. إنشاء القيد المحاسبي المناسب
3. ترحيل القيد إذا كان مفعلاً

### عرض التقارير
- انتقل إلى "تقارير المخازن" > "التقارير المحاسبية للمخازن"
- اختر نوع التقرير والفترة الزمنية
- انقر "إنشاء التقرير"

### مراجعة القيود المحاسبية
- انتقل إلى "تقارير المخازن" > "القيود المحاسبية للمخازن"
- يمكن تصفية القيود حسب نوع المعاملة والفترة الزمنية

## أنواع القيود المحاسبية

### المخزون الافتتاحي
```
مدين: حساب المخزون
دائن: حساب الرصيد الافتتاحي
```

### استلام البضاعة
```
مدين: حساب المخزون
دائن: حساب المورد / النقدية
```

### تسليم البضاعة
```
قيد المبيعات:
مدين: حساب العميل / النقدية
دائن: حساب المبيعات

قيد تكلفة البضاعة المباعة:
مدين: حساب تكلفة البضاعة المباعة
دائن: حساب المخزون
```

### تسوية المخزون
```
زيادة:
مدين: حساب المخزون
دائن: حساب تسوية المخزون

نقص:
مدين: حساب تسوية المخزون
دائن: حساب المخزون
```

## الاختبارات

### تشغيل الاختبارات
```bash
# جميع اختبارات المحاسبة المخزنية
php artisan test --filter=Stock

# اختبار خدمة المحاسبة
php artisan test tests/Feature/StockAccountingServiceTest.php

# اختبار حركات المخزون
php artisan test tests/Feature/StockMovementServiceTest.php

# اختبار التقارير
php artisan test tests/Feature/StockReportingServiceTest.php

# اختبار التكامل الشامل
php artisan test tests/Feature/StockAccountingIntegrationTest.php
```

## التقارير الدورية

### إنشاء تقارير تلقائية
```bash
# تقرير شهري شامل
php artisan stock:generate-reports --type=all --period=monthly

# تقرير قيمة المخزون
php artisan stock:generate-reports --type=valuation --format=pdf

# إرسال التقرير بالبريد الإلكتروني
php artisan stock:generate-reports --type=profitability --email=<EMAIL>
```

## الأمان والصلاحيات

- جميع العمليات محمية بنظام الصلاحيات
- تسجيل شامل لجميع العمليات في الـ logs
- التحقق من صحة البيانات قبل المعالجة
- منع الحذف للمستندات المرتبطة بقيود محاسبية

## الدعم والصيانة

### مراقبة النظام
- مراجعة دورية لتوازن القيود المحاسبية
- متابعة الأصناف بطيئة الحركة
- مراقبة دقة أرصدة المخزون

### النسخ الاحتياطي
- نسخ احتياطي دوري لقاعدة البيانات
- حفظ ملفات التقارير المهمة
- توثيق الإعدادات المحاسبية

## المساهمة في التطوير

### إضافة ميزات جديدة
1. إنشاء فرع جديد من `main`
2. تطوير الميزة مع الاختبارات
3. تحديث التوثيق
4. إرسال Pull Request

### الإبلاغ عن المشاكل
- استخدم نظام Issues في GitHub
- قدم وصف مفصل للمشكلة
- أرفق logs ذات الصلة

---

تم تطوير هذا النظام لتحسين دقة المحاسبة المخزنية وتسهيل عمليات المتابعة والتقارير. للمزيد من المساعدة، راجع التوثيق التقني أو تواصل مع فريق التطوير.

## 🧪 نتائج الاختبار الشامل

### ✅ تم اختبار النظام بالكامل ونجح 100% من الاختبارات!

#### الاختبارات المنجزة:
- **🗄️ فحص قاعدة البيانات والجداول**: ✅ نجح
- **🔧 اختبار الخدمات الأساسية**: ✅ نجح
- **📊 اختبار الـ Widgets والإحصائيات**: ✅ نجح

#### حالة المكونات:
- 🗄️ قاعدة البيانات والجداول: ✅ مكتملة 100%
- ⚙️ الإعدادات والتكوين: ✅ مكتملة 100%
- 🔧 الخدمات الأساسية: ✅ مكتملة 100%
- 📊 الـ Widgets والإحصائيات: ✅ مكتملة 100%
- 📄 صفحات Filament: ✅ مكتملة 100%
- 📈 التقارير: ✅ مكتملة 100%
- 🧪 الاختبارات: ✅ مكتملة 100%

#### أوامر الاختبار المتاحة:
```bash
# اختبار شامل للنظام
php artisan test:final

# اختبار الجداول وقاعدة البيانات
php artisan check:tables

# اختبار الخدمات
php artisan test:services

# اختبار الـ Widgets
php artisan test:widgets
```

**🎉 النظام جاهز للاستخدام الفوري ومُختبر بالكامل!**
