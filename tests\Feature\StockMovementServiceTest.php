<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\StockMovementService;
use App\Models\Stocks\StockDoc;
use App\Models\Stocks\StockDocItem;
use App\Models\Stocks\StockLocation;
use App\Models\Stocks\Stock;
use App\Models\Items\Item;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class StockMovementServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private StockMovementService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(StockMovementService::class);
    }

    /** @test */
    public function it_updates_stock_balance_for_opening_stock(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 100,
            'cost' => 50,
        ]);

        // تنفيذ الاختبار
        $result = $this->service->processStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $stock = Stock::where('item_id', $item->id)
                     ->where('location_id', $location->id)
                     ->first();
        
        $this->assertNotNull($stock);
        $this->assertEquals(100, $stock->quantity);
    }

    /** @test */
    public function it_increases_stock_balance_for_receipt(): void
    {
        // إعداد رصيد موجود
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $existingStock = Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 50,
        ]);

        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 30,
            'cost' => 25,
        ]);

        // تنفيذ الاختبار
        $result = $this->service->processStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $existingStock->refresh();
        $this->assertEquals(80, $existingStock->quantity); // 50 + 30
    }

    /** @test */
    public function it_decreases_stock_balance_for_delivery(): void
    {
        // إعداد رصيد موجود
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $existingStock = Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 100,
        ]);

        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::DELIVERY,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 25,
            'cost' => 30,
            'price' => 50,
        ]);

        // تنفيذ الاختبار
        $result = $this->service->processStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $existingStock->refresh();
        $this->assertEquals(75, $existingStock->quantity); // 100 - 25
    }

    /** @test */
    public function it_throws_exception_for_insufficient_stock(): void
    {
        // إعداد رصيد غير كافي
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $existingStock = Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 10,
        ]);

        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::DELIVERY,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 20, // أكثر من المتاح
            'cost' => 30,
            'price' => 50,
        ]);

        // توقع استثناء
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('الرصيد المتاح للصنف');

        $this->service->processStockDocument($stockDoc);
    }

    /** @test */
    public function it_handles_positive_adjustment(): void
    {
        // إعداد رصيد موجود
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $existingStock = Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 50,
        ]);

        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::ADJUSTMENT,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 15, // زيادة
            'cost' => 40,
        ]);

        // تنفيذ الاختبار
        $result = $this->service->processStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $existingStock->refresh();
        $this->assertEquals(65, $existingStock->quantity); // 50 + 15
    }

    /** @test */
    public function it_handles_negative_adjustment(): void
    {
        // إعداد رصيد موجود
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $existingStock = Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 50,
        ]);

        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::ADJUSTMENT,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => -10, // نقص
            'cost' => 40,
        ]);

        // تنفيذ الاختبار
        $result = $this->service->processStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $existingStock->refresh();
        $this->assertEquals(40, $existingStock->quantity); // 50 - 10
    }

    /** @test */
    public function it_gets_item_stock_balance(): void
    {
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 75,
        ]);

        $balance = $this->service->getItemStockBalance($item->id, $location->id);
        
        $this->assertEquals(75, $balance);
    }

    /** @test */
    public function it_gets_total_item_stock(): void
    {
        $item = Item::factory()->create();
        $location1 = StockLocation::factory()->create();
        $location2 = StockLocation::factory()->create();
        
        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location1->id,
            'quantity' => 30,
        ]);

        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location2->id,
            'quantity' => 45,
        ]);

        $totalStock = $this->service->getTotalItemStock($item->id);
        
        $this->assertEquals(75, $totalStock); // 30 + 45
    }

    /** @test */
    public function it_generates_location_stock_report(): void
    {
        $location = StockLocation::factory()->create();
        $item1 = Item::factory()->create(['name' => 'صنف 1', 'cost' => 100]);
        $item2 = Item::factory()->create(['name' => 'صنف 2', 'cost' => 200]);
        
        Stock::factory()->create([
            'item_id' => $item1->id,
            'location_id' => $location->id,
            'quantity' => 10,
        ]);

        Stock::factory()->create([
            'item_id' => $item2->id,
            'location_id' => $location->id,
            'quantity' => 5,
        ]);

        $report = $this->service->getLocationStockReport($location->id);
        
        $this->assertCount(2, $report);
        $this->assertEquals('صنف 1', $report[0]['item_name']);
        $this->assertEquals(10, $report[0]['quantity']);
        $this->assertEquals(1000, $report[0]['total_value']); // 10 * 100
    }

    /** @test */
    public function it_generates_item_stock_report(): void
    {
        $item = Item::factory()->create();
        $location1 = StockLocation::factory()->create(['name' => 'موقع 1']);
        $location2 = StockLocation::factory()->create(['name' => 'موقع 2']);
        
        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location1->id,
            'quantity' => 20,
        ]);

        Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location2->id,
            'quantity' => 15,
        ]);

        $report = $this->service->getItemStockReport($item->id);
        
        $this->assertCount(2, $report);
        $this->assertEquals('موقع 1', $report[0]['location_name']);
        $this->assertEquals(20, $report[0]['quantity']);
    }

    /** @test */
    public function it_reverses_stock_document(): void
    {
        // إعداد رصيد موجود
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $existingStock = Stock::factory()->create([
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity' => 100,
        ]);

        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 30,
            'cost' => 25,
        ]);

        // معالجة المستند أولاً
        $this->service->processStockDocument($stockDoc);
        $existingStock->refresh();
        $this->assertEquals(130, $existingStock->quantity);

        // عكس المستند
        $result = $this->service->reverseStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $existingStock->refresh();
        $this->assertEquals(100, $existingStock->quantity); // العودة للرصيد الأصلي
    }

    /** @test */
    public function it_handles_multiple_items_in_document(): void
    {
        $location = StockLocation::factory()->create();
        $item1 = Item::factory()->create();
        $item2 = Item::factory()->create();
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item1->id,
            'quantity' => 50,
            'cost' => 100,
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item2->id,
            'quantity' => 25,
            'cost' => 200,
        ]);

        // تنفيذ الاختبار
        $result = $this->service->processStockDocument($stockDoc);

        // التحقق من النتائج
        $this->assertTrue($result);
        
        $stock1 = Stock::where('item_id', $item1->id)->where('location_id', $location->id)->first();
        $stock2 = Stock::where('item_id', $item2->id)->where('location_id', $location->id)->first();
        
        $this->assertEquals(50, $stock1->quantity);
        $this->assertEquals(25, $stock2->quantity);
    }
}
