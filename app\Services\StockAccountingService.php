<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\LineItem;
use App\Models\Stocks\StockDoc;
use App\Models\Stocks\StockDocItem;
use App\Models\Stocks\StockLocation;
use App\Models\Items\Item;
use App\Settings\StockAccountingSettings;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * خدمة المحاسبة للمخازن
 * تتعامل مع إنشاء القيود المحاسبية التلقائية لحركات المخزون
 */
class StockAccountingService
{
    private ?StockAccountingSettings $settings = null;

    public function __construct()
    {
        // لا نحمل الإعدادات في الـ constructor لتجنب المشاكل
    }

    /**
     * الحصول على الإعدادات مع التحقق من وجودها
     */
    private function getSettings(): StockAccountingSettings
    {
        if ($this->settings === null) {
            try {
                $this->settings = app(StockAccountingSettings::class);
            } catch (\Exception $e) {
                // إنشاء إعدادات افتراضية إذا لم تكن موجودة
                $this->ensureSettingsExist();
                $this->settings = app(StockAccountingSettings::class);
            }
        }

        return $this->settings;
    }

    /**
     * إنشاء قيد محاسبي لحركة المخزون
     */
    public function createJournalEntry(StockDoc $stockDoc): ?int
    {
        try {
            $settings = $this->getSettings();

            if (!$settings->auto_create_journal_entries) {
                return null;
            }

            // إنشاء القيد المحاسبي
            $transaction = new \IFRS\Models\Transaction([
                'account_id' => $settings->default_inventory_account,
                'transaction_date' => $stockDoc->transaction_date,
                'narration' => $stockDoc->description ?? 'حركة مخزون',
                'reference' => $stockDoc->ref_id,
                'entity_id' => $stockDoc->entity_id,
            ]);

            $transaction->save();

            // إضافة بنود القيد
            foreach ($stockDoc->items as $item) {
                $this->addJournalEntryLines($transaction, $item, $stockDoc->operation_type);
            }

            // ربط القيد بمستند المخزون
            $stockDoc->update(['journal_entry_id' => $transaction->id]);

            return $transaction->id;

        } catch (\Exception $e) {
            Log::error('خطأ في إنشاء القيد المحاسبي: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * حساب قيمة المخزون
     */
    public function calculateInventoryValue(int $itemId = null, int $locationId = null): float
    {
        try {
            $query = DB::table('inv_stocks as s')
                ->join('inv_items as i', 's.item_id', '=', 'i.id')
                ->selectRaw('SUM(s.quantity * i.cost) as total_value');

            if ($itemId) {
                $query->where('s.item_id', $itemId);
            }

            if ($locationId) {
                $query->where('s.location_id', $locationId);
            }

            $result = $query->first();

            return $result->total_value ?? 0.0;

        } catch (\Exception $e) {
            Log::error('خطأ في حساب قيمة المخزون: ' . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * التأكد من وجود الإعدادات
     */
    private function ensureSettingsExist(): void
    {
        $companyId = auth()->user()->company_id ?? 1;
        $group = 'stock_accounting_' . $companyId;

        $exists = DB::table('settings')
            ->where('group', $group)
            ->where('name', 'stock_accounting_settings')
            ->exists();

        if (!$exists) {
            $defaultSettings = [
                'default_inventory_account' => null,
                'inventory_adjustment_account' => null,
                'cost_of_goods_sold_account' => null,
                'sales_account' => null,
                'purchases_account' => null,
                'opening_balance_account' => null,
                'retained_earnings_account' => null,
                'cash_account' => null,
                'bank_account' => null,
                'default_customer_account' => null,
                'default_supplier_account' => null,
                'accounts_receivable_account' => null,
                'accounts_payable_account' => null,
                'auto_create_journal_entries' => true,
                'create_cogs_entries' => true,
                'separate_location_accounts' => false,
                'inventory_valuation_method' => 'fifo',
                'use_standard_cost' => false,
                'input_tax_account' => null,
                'output_tax_account' => null,
                'include_tax_in_inventory_cost' => true
            ];

            DB::table('settings')->insert([
                'group' => $group,
                'name' => 'stock_accounting_settings',
                'locked' => false,
                'payload' => json_encode($defaultSettings),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    /**
     * إنشاء قيد محاسبي لحركة مخزنية
     */
    public function createStockJournalEntry(StockDoc $stockDoc): ?JournalEntry
    {
        try {
            DB::beginTransaction();

            $journalEntry = $this->createBaseJournalEntry($stockDoc);

            switch ($stockDoc->transaction_type) {
                case StockDoc::OPENING:
                    $this->handleOpeningStock($journalEntry, $stockDoc);
                    break;

                case StockDoc::RECEIPT:
                    $this->handleReceiptStock($journalEntry, $stockDoc);
                    break;

                case StockDoc::DELIVERY:
                    $this->handleDeliveryStock($journalEntry, $stockDoc);
                    break;

                case StockDoc::ADJUSTMENT:
                    $this->handleAdjustmentStock($journalEntry, $stockDoc);
                    break;

                default:
                    throw new \Exception('نوع حركة مخزنية غير مدعوم: ' . $stockDoc->transaction_type);
            }

            $journalEntry->post();

            // ربط القيد بالمستند المخزني
            $stockDoc->update(['journal_entry_id' => $journalEntry->id]);

            DB::commit();

            Log::info('تم إنشاء قيد محاسبي للمستند المخزني', [
                'stock_doc_id' => $stockDoc->id,
                'journal_entry_id' => $journalEntry->id,
                'transaction_type' => $stockDoc->transaction_type
            ]);

            return $journalEntry;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في إنشاء القيد المحاسبي للمخزون', [
                'stock_doc_id' => $stockDoc->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * إنشاء القيد الأساسي
     */
    private function createBaseJournalEntry(StockDoc $stockDoc): JournalEntry
    {
        // إنشاء معاملة بدون حساب رئيسي (سيتم تحديده تلقائياً)
        return JournalEntry::create([
            'date' => $stockDoc->transaction_date ?? Carbon::now(),
            'narration' => $this->generateNarration($stockDoc),
            'reference' => $this->generateReference($stockDoc),
        ]);
    }

    /**
     * معالجة المخزون الافتتاحي
     */
    private function handleOpeningStock(JournalEntry $journalEntry, StockDoc $stockDoc): void
    {
        // معالجة كل صنف على حدة للحصول على الحسابات المناسبة
        foreach ($stockDoc->items as $stockDocItem) {
            $item = $stockDocItem->item;
            $itemValue = $stockDocItem->quantity * $stockDocItem->cost;

            // مدين: حساب المخزون للصنف
            $journalEntry->addLineItem(
                LineItem::create([
                    'account_id' => $this->getInventoryAccount($stockDoc->location, $item),
                    'narration' => "مخزون افتتاحي - {$item->name}",
                    'amount' => $itemValue,
                    'credited' => false
                ])
            );
        }

        // دائن: حساب رأس المال أو الأرباح المحتجزة (إجمالي)
        $totalValue = $this->calculateStockDocTotalValue($stockDoc);
        $journalEntry->addLineItem(
            LineItem::create([
                'account_id' => $this->getOpeningBalanceAccount(),
                'narration' => 'رصيد افتتاحي للمخزون',
                'amount' => $totalValue,
                'credited' => true
            ])
        );
    }

    /**
     * معالجة استلام البضاعة
     */
    private function handleReceiptStock(JournalEntry $journalEntry, StockDoc $stockDoc): void
    {
        // معالجة كل صنف على حدة
        foreach ($stockDoc->items as $stockDocItem) {
            $item = $stockDocItem->item;
            $itemValue = $stockDocItem->quantity * $stockDocItem->cost;

            // مدين: حساب المخزون للصنف
            $journalEntry->addLineItem(
                LineItem::create([
                    'account_id' => $this->getInventoryAccount($stockDoc->location, $item),
                    'narration' => "استلام بضاعة - {$item->name}",
                    'amount' => $itemValue,
                    'credited' => false
                ])
            );
        }

        // دائن: حساب المورد أو النقدية (إجمالي)
        $totalValue = $this->calculateStockDocTotalValue($stockDoc);
        $creditAccount = $stockDoc->party_id
            ? $this->getSupplierAccount($stockDoc->party_id)
            : $this->getCashAccount();

        $journalEntry->addLineItem(
            LineItem::create([
                'account_id' => $creditAccount,
                'narration' => $stockDoc->party_id
                    ? 'مشتريات من مورد'
                    : 'مشتريات نقدية',
                'amount' => $totalValue,
                'credited' => true
            ])
        );
    }

    /**
     * معالجة تسليم البضاعة
     */
    private function handleDeliveryStock(JournalEntry $journalEntry, StockDoc $stockDoc): void
    {
        $totalSales = $this->calculateStockDocTotalSales($stockDoc);

        // مدين: حساب العميل أو النقدية (إجمالي)
        $debitAccount = $stockDoc->party_id
            ? $this->getCustomerAccount($stockDoc->party_id)
            : $this->getCashAccount();

        $journalEntry->addLineItem(
            LineItem::create([
                'account_id' => $debitAccount,
                'narration' => $stockDoc->party_id
                    ? 'مبيعات لعميل'
                    : 'مبيعات نقدية',
                'amount' => $totalSales,
                'credited' => false
            ])
        );

        // دائن: حساب المبيعات لكل صنف
        foreach ($stockDoc->items as $stockDocItem) {
            $item = $stockDocItem->item;
            $itemSalesValue = $stockDocItem->quantity * $stockDocItem->price;

            $salesAccount = $item->getSalesAccountId() ?? $this->getSalesAccount();

            $journalEntry->addLineItem(
                LineItem::create([
                    'account_id' => $salesAccount,
                    'narration' => "مبيعات - {$item->name}",
                    'amount' => $itemSalesValue,
                    'credited' => true
                ])
            );
        }

        // قيد تكلفة البضاعة المباعة
        $this->createCostOfGoodsSoldEntry($stockDoc);
    }

    /**
     * معالجة تسوية المخزون
     */
    private function handleAdjustmentStock(JournalEntry $journalEntry, StockDoc $stockDoc): void
    {
        // معالجة كل صنف على حدة
        foreach ($stockDoc->items as $stockDocItem) {
            $item = $stockDocItem->item;
            $adjustmentValue = $stockDocItem->quantity * $stockDocItem->cost;

            if ($adjustmentValue > 0) {
                // زيادة في المخزون
                $journalEntry->addLineItem(
                    LineItem::create([
                        'account_id' => $this->getInventoryAccount($stockDoc->location, $item),
                        'narration' => "تسوية مخزون - زيادة - {$item->name}",
                        'amount' => $adjustmentValue,
                        'credited' => false
                    ])
                );

                $journalEntry->addLineItem(
                    LineItem::create([
                        'account_id' => $this->getInventoryAdjustmentAccount(),
                        'narration' => "تسوية مخزون - زيادة - {$item->name}",
                        'amount' => $adjustmentValue,
                        'credited' => true
                    ])
                );
            } else {
                // نقص في المخزون
                $adjustmentValue = abs($adjustmentValue);

                $journalEntry->addLineItem(
                    LineItem::create([
                        'account_id' => $this->getInventoryAdjustmentAccount(),
                        'narration' => "تسوية مخزون - نقص - {$item->name}",
                        'amount' => $adjustmentValue,
                        'credited' => false
                    ])
                );

                $journalEntry->addLineItem(
                    LineItem::create([
                        'account_id' => $this->getInventoryAccount($stockDoc->location, $item),
                        'narration' => "تسوية مخزون - نقص - {$item->name}",
                        'amount' => $adjustmentValue,
                        'credited' => true
                    ])
                );
            }
        }
    }

    /**
     * إنشاء قيد تكلفة البضاعة المباعة
     */
    private function createCostOfGoodsSoldEntry(StockDoc $stockDoc): JournalEntry
    {
        $cogsEntry = JournalEntry::create([
            'account_id' => $this->getCostOfGoodsSoldAccount(),
            'date' => $stockDoc->transaction_date ?? Carbon::now(),
            'narration' => "تكلفة البضاعة المباعة - {$stockDoc->id}",
            'reference' => "COGS-{$stockDoc->id}",
            'currency_id' => Auth::user()->entity->currency_id ?? 1, // العملة الافتراضية
        ]);

        // معالجة كل صنف على حدة
        foreach ($stockDoc->items as $stockDocItem) {
            $item = $stockDocItem->item;
            $itemCost = $stockDocItem->quantity * $item->cost;

            // مدين: تكلفة البضاعة المباعة للصنف
            $cogsAccount = $item->getCogsAccountId() ?? $this->getCostOfGoodsSoldAccount();
            $cogsEntry->addLineItem(
                LineItem::create([
                    'account_id' => $cogsAccount,
                    'narration' => "تكلفة البضاعة المباعة - {$item->name}",
                    'amount' => $itemCost,
                    'credited' => false
                ])
            );

            // دائن: المخزون للصنف
            $cogsEntry->addLineItem(
                LineItem::create([
                    'account_id' => $this->getInventoryAccount($stockDoc->location, $item),
                    'narration' => "خصم من المخزون - {$item->name}",
                    'amount' => $itemCost,
                    'credited' => true
                ])
            );
        }

        $cogsEntry->post();
        return $cogsEntry;
    }

    /**
     * حساب القيمة الإجمالية للمستند المخزني
     */
    private function calculateStockDocTotalValue(StockDoc $stockDoc): float
    {
        return $stockDoc->items->sum(function (StockDocItem $item) {
            return $item->quantity * $item->cost;
        });
    }

    /**
     * حساب التكلفة الإجمالية للمستند المخزني
     */
    private function calculateStockDocTotalCost(StockDoc $stockDoc): float
    {
        return $stockDoc->items->sum(function (StockDocItem $item) {
            return $item->quantity * $item->item->cost;
        });
    }

    /**
     * حساب قيمة المبيعات الإجمالية للمستند المخزني
     */
    private function calculateStockDocTotalSales(StockDoc $stockDoc): float
    {
        return $stockDoc->items->sum(function (StockDocItem $item) {
            return $item->quantity * $item->price;
        });
    }

    /**
     * حساب قيمة التسوية للمستند المخزني
     */
    private function calculateStockDocAdjustmentValue(StockDoc $stockDoc): float
    {
        return $stockDoc->items->sum(function (StockDocItem $item) {
            return $item->quantity * $item->cost;
        });
    }

    /**
     * توليد وصف القيد
     */
    private function generateNarration(StockDoc $stockDoc): string
    {
        $types = [
            StockDoc::OPENING => 'مخزون افتتاحي',
            StockDoc::RECEIPT => 'استلام بضاعة',
            StockDoc::DELIVERY => 'تسليم بضاعة',
            StockDoc::ADJUSTMENT => 'تسوية مخزون',
        ];

        return ($types[$stockDoc->transaction_type] ?? 'حركة مخزنية') . ' - ' . $stockDoc->id;
    }

    /**
     * توليد مرجع القيد
     */
    private function generateReference(StockDoc $stockDoc): string
    {
        return 'STOCK-' . $stockDoc->transaction_type . '-' . $stockDoc->id;
    }

    // الحسابات المحاسبية
    private function getMainInventoryAccount(StockLocation $location): int
    {
        if ($this->getSettings()->useSeparateLocationAccounts() && $location->account_id) {
            return $location->account_id;
        }
        return $this->getDefaultInventoryAccount();
    }

    private function getInventoryAccount(StockLocation $location, Item $item = null): int
    {
        // أولوية للحساب المحدد للصنف
        if ($item && $item->getInventoryAccountId()) {
            return $item->getInventoryAccountId();
        }

        // ثم حساب الموقع إذا كان مفعلاً
        if ($this->getSettings()->useSeparateLocationAccounts() && $location->account_id) {
            return $location->account_id;
        }

        // أخيراً الحساب الافتراضي
        return $this->getDefaultInventoryAccount();
    }

    private function getDefaultInventoryAccount(): int
    {
        $account = $this->getSettings()->getDefaultInventoryAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب المخزون الافتراضي في الإعدادات');
        }
        return $account;
    }

    private function getOpeningBalanceAccount(): int
    {
        $account = $this->getSettings()->getOpeningBalanceAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب الرصيد الافتتاحي في الإعدادات');
        }
        return $account;
    }

    private function getStockControlAccount(): int
    {
        $settings = $this->getSettings();

        // محاولة الحصول على حساب التحكم من الإعدادات
        if (isset($settings->stock_control_account) && $settings->stock_control_account) {
            return $settings->stock_control_account;
        }

        // البحث عن حساب تحكم موجود
        $controlAccount = \App\Models\Account::where('account_type', 'CONTROL')->first();

        if ($controlAccount) {
            return $controlAccount->id;
        }

        // استخدام حساب الأرصدة الافتتاحية كبديل
        return $this->getOpeningBalanceAccount();
    }

    private function getSupplierAccount(int $supplierId): int
    {
        // البحث عن حساب المورد المحدد أو استخدام الحساب الافتراضي
        $account = $this->getSettings()->getDefaultSupplierAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب الموردين الافتراضي في الإعدادات');
        }
        return $account;
    }

    private function getCustomerAccount(int $customerId): int
    {
        // البحث عن حساب العميل المحدد أو استخدام الحساب الافتراضي
        $account = $this->getSettings()->getDefaultCustomerAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب العملاء الافتراضي في الإعدادات');
        }
        return $account;
    }

    private function getCashAccount(): int
    {
        $account = $this->getSettings()->getCashAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد الحساب النقدي في الإعدادات');
        }
        return $account;
    }

    private function getSalesAccount(): int
    {
        $account = $this->getSettings()->getSalesAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب المبيعات في الإعدادات');
        }
        return $account;
    }

    private function getCostOfGoodsSoldAccount(): int
    {
        $account = $this->getSettings()->getCostOfGoodsSoldAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب تكلفة البضاعة المباعة في الإعدادات');
        }
        return $account;
    }

    private function getInventoryAdjustmentAccount(): int
    {
        $account = $this->getSettings()->getInventoryAdjustmentAccount();
        if (!$account) {
            throw new \Exception('لم يتم تحديد حساب تسوية المخزون في الإعدادات');
        }
        return $account;
    }
}
