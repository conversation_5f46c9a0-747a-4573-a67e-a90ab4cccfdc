<?php

namespace App\Filament\Widgets;

use App\Models\Stocks\StockDoc;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class StockMovementChartWidget extends ChartWidget
{
    protected static ?string $heading = 'حركات المخزون الشهرية';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        // الحصول على بيانات آخر 12 شهر
        $months = [];
        $receiptsData = [];
        $deliveriesData = [];
        $adjustmentsData = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');

            // عدد مستندات الاستلام
            $receipts = StockDoc::where('transaction_type', StockDoc::RECEIPT)
                ->whereMonth('transaction_date', $date->month)
                ->whereYear('transaction_date', $date->year)
                ->count();

            // عدد مستندات التسليم
            $deliveries = StockDoc::where('transaction_type', StockDoc::DELIVERY)
                ->whereMonth('transaction_date', $date->month)
                ->whereYear('transaction_date', $date->year)
                ->count();

            // عدد مستندات التسوية
            $adjustments = StockDoc::where('transaction_type', StockDoc::ADJUSTMENT)
                ->whereMonth('transaction_date', $date->month)
                ->whereYear('transaction_date', $date->year)
                ->count();

            $receiptsData[] = $receipts;
            $deliveriesData[] = $deliveries;
            $adjustmentsData[] = $adjustments;
        }

        return [
            'datasets' => [
                [
                    'label' => 'استلام',
                    'data' => $receiptsData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.2)',
                    'borderColor' => 'rgba(34, 197, 94, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'تسليم',
                    'data' => $deliveriesData,
                    'backgroundColor' => 'rgba(251, 146, 60, 0.2)',
                    'borderColor' => 'rgba(251, 146, 60, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'تسوية',
                    'data' => $adjustmentsData,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.2)',
                    'borderColor' => 'rgba(239, 68, 68, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
            'maintainAspectRatio' => false,
        ];
    }
}
