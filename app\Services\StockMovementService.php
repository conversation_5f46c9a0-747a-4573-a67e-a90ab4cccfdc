<?php

namespace App\Services;

use App\Models\Stocks\StockDoc;
use App\Models\Stocks\StockDocItem;
use App\Models\Stocks\Stock;
use App\Models\Items\Item;
use App\Models\Stocks\StockLocation;
use App\Settings\StockAccountingSettings;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * خدمة حركات المخزون
 * تتعامل مع تحديث أرصدة المخزون وإنشاء الحركات
 */
class StockMovementService
{
    private StockAccountingService $accountingService;
    private StockAccountingSettings $settings;

    public function __construct()
    {
        $this->accountingService = app(StockAccountingService::class);
        $this->settings = app(StockAccountingSettings::class);
    }

    /**
     * معالجة حركة مخزون
     */
    public function processStockMovement(StockDoc $stockDoc): bool
    {
        return $this->processStockDocument($stockDoc);
    }

    /**
     * تحديث كمية المخزون
     */
    public function updateStockQuantity(int $itemId, int $locationId, float $quantity, string $operation = 'add'): bool
    {
        try {
            DB::beginTransaction();

            $stock = Stock::firstOrCreate([
                'item_id' => $itemId,
                'location_id' => $locationId,
                'entity_id' => auth()->user()->entity_id ?? 1,
            ], [
                'quantity' => 0,
                'user_id' => auth()->id(),
            ]);

            if ($operation === 'add') {
                $stock->quantity += $quantity;
            } elseif ($operation === 'subtract') {
                $stock->quantity -= $quantity;
            } else {
                $stock->quantity = $quantity;
            }

            $stock->save();

            DB::commit();

            Log::info('تم تحديث كمية المخزون', [
                'item_id' => $itemId,
                'location_id' => $locationId,
                'quantity' => $quantity,
                'operation' => $operation,
                'new_quantity' => $stock->quantity
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('خطأ في تحديث كمية المخزون: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * معالجة مستند مخزني جديد
     */
    public function processStockDocument(StockDoc $stockDoc): bool
    {
        try {
            DB::beginTransaction();

            // تحديث أرصدة المخزون
            $this->updateStockBalances($stockDoc);

            // إنشاء القيد المحاسبي إذا كان مفعلاً
            if ($this->settings->isAutoJournalEntriesEnabled()) {
                $this->accountingService->createStockJournalEntry($stockDoc);
            }

            DB::commit();

            Log::info('تم معالجة المستند المخزني بنجاح', [
                'stock_doc_id' => $stockDoc->id,
                'transaction_type' => $stockDoc->transaction_type,
                'items_count' => $stockDoc->items->count()
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('فشل في معالجة المستند المخزني', [
                'stock_doc_id' => $stockDoc->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * تحديث أرصدة المخزون بناءً على نوع المستند
     */
    private function updateStockBalances(StockDoc $stockDoc): void
    {
        foreach ($stockDoc->items as $stockDocItem) {
            $this->updateItemStockBalance($stockDocItem, $stockDoc);
        }
    }

    /**
     * تحديث رصيد صنف محدد
     */
    private function updateItemStockBalance(StockDocItem $stockDocItem, StockDoc $stockDoc): void
    {
        $item = $stockDocItem->item;
        $location = $stockDoc->location;
        
        // البحث عن رصيد الصنف في الموقع أو إنشاؤه
        $stock = Stock::firstOrCreate([
            'item_id' => $item->id,
            'location_id' => $location->id,
        ], [
            'quantity' => 0,
            'entity_id' => $stockDoc->entity_id,
            'user_id' => $stockDoc->user_id,
        ]);

        // حساب التغيير في الكمية بناءً على نوع المستند
        $quantityChange = $this->calculateQuantityChange($stockDocItem, $stockDoc);

        // تحديث الرصيد
        $newQuantity = $stock->quantity + $quantityChange;
        
        // التحقق من عدم السماح بالرصيد السالب (اختياري)
        if ($newQuantity < 0 && !$this->allowNegativeStock()) {
            throw new \Exception("الرصيد المتاح للصنف '{$item->name}' غير كافي. الرصيد الحالي: {$stock->quantity}، المطلوب: " . abs($quantityChange));
        }

        $stock->update(['quantity' => $newQuantity]);

        Log::info('تم تحديث رصيد المخزون', [
            'item_id' => $item->id,
            'item_name' => $item->name,
            'location_id' => $location->id,
            'location_name' => $location->name,
            'old_quantity' => $stock->quantity - $quantityChange,
            'quantity_change' => $quantityChange,
            'new_quantity' => $newQuantity,
            'transaction_type' => $stockDoc->transaction_type
        ]);
    }

    /**
     * حساب التغيير في الكمية بناءً على نوع المستند
     */
    private function calculateQuantityChange(StockDocItem $stockDocItem, StockDoc $stockDoc): float
    {
        $quantity = $stockDocItem->quantity;

        switch ($stockDoc->transaction_type) {
            case StockDoc::OPENING:
            case StockDoc::RECEIPT:
                // زيادة في المخزون
                return $quantity;

            case StockDoc::DELIVERY:
                // نقص من المخزون
                return -$quantity;

            case StockDoc::ADJUSTMENT:
                // تسوية - يمكن أن تكون زيادة أو نقص
                // إذا كانت الكمية موجبة = زيادة، إذا كانت سالبة = نقص
                return $quantity;

            default:
                throw new \Exception("نوع مستند غير مدعوم: {$stockDoc->transaction_type}");
        }
    }

    /**
     * التحقق من السماح بالرصيد السالب
     */
    private function allowNegativeStock(): bool
    {
        // يمكن إضافة هذا كإعداد في المستقبل
        return false;
    }

    /**
     * الحصول على رصيد صنف في موقع محدد
     */
    public function getItemStockBalance(int $itemId, int $locationId): float
    {
        $stock = Stock::where('item_id', $itemId)
                     ->where('location_id', $locationId)
                     ->first();

        return $stock ? $stock->quantity : 0;
    }

    /**
     * الحصول على إجمالي رصيد صنف في جميع المواقع
     */
    public function getTotalItemStock(int $itemId): float
    {
        return Stock::where('item_id', $itemId)->sum('quantity');
    }

    /**
     * الحصول على تقرير أرصدة المخزون لموقع محدد
     */
    public function getLocationStockReport(int $locationId): array
    {
        $stocks = Stock::with(['item'])
                      ->where('location_id', $locationId)
                      ->where('quantity', '>', 0)
                      ->get();

        return $stocks->map(function ($stock) {
            return [
                'item_id' => $stock->item_id,
                'item_name' => $stock->item->name,
                'item_sku' => $stock->item->sku_code,
                'quantity' => $stock->quantity,
                'cost' => $stock->item->cost,
                'total_value' => $stock->quantity * $stock->item->cost,
            ];
        })->toArray();
    }

    /**
     * الحصول على تقرير أرصدة المخزون لصنف محدد
     */
    public function getItemStockReport(int $itemId): array
    {
        $stocks = Stock::with(['location'])
                      ->where('item_id', $itemId)
                      ->where('quantity', '>', 0)
                      ->get();

        return $stocks->map(function ($stock) {
            return [
                'location_id' => $stock->location_id,
                'location_name' => $stock->location->name,
                'quantity' => $stock->quantity,
            ];
        })->toArray();
    }

    /**
     * تحديث تكلفة الصنف بناءً على طريقة التقييم
     */
    public function updateItemCost(Item $item, float $newCost, float $quantity): void
    {
        $valuationMethod = $item->getValuationMethod();

        switch ($valuationMethod) {
            case 'fifo':
                // First In, First Out - لا نحدث التكلفة للأصناف الموجودة
                break;

            case 'lifo':
                // Last In, First Out - لا نحدث التكلفة للأصناف الموجودة
                break;

            case 'average':
                // المتوسط المرجح
                $this->updateAverageCost($item, $newCost, $quantity);
                break;

            default:
                Log::warning("طريقة تقييم غير مدعومة: {$valuationMethod}");
        }
    }

    /**
     * تحديث التكلفة بطريقة المتوسط المرجح
     */
    private function updateAverageCost(Item $item, float $newCost, float $newQuantity): void
    {
        $currentQuantity = $this->getTotalItemStock($item->id);
        $currentCost = $item->cost;

        if ($currentQuantity > 0) {
            $totalValue = ($currentQuantity * $currentCost) + ($newQuantity * $newCost);
            $totalQuantity = $currentQuantity + $newQuantity;
            $averageCost = $totalValue / $totalQuantity;

            $item->update(['cost' => $averageCost]);

            Log::info('تم تحديث التكلفة بطريقة المتوسط المرجح', [
                'item_id' => $item->id,
                'old_cost' => $currentCost,
                'new_cost' => $averageCost,
                'current_quantity' => $currentQuantity,
                'new_quantity' => $newQuantity
            ]);
        }
    }

    /**
     * إلغاء مستند مخزني
     */
    public function reverseStockDocument(StockDoc $stockDoc): bool
    {
        try {
            DB::beginTransaction();

            // عكس حركات المخزون
            foreach ($stockDoc->items as $stockDocItem) {
                $this->reverseItemStockBalance($stockDocItem, $stockDoc);
            }

            // إلغاء القيد المحاسبي إذا وجد
            if ($stockDoc->journal_entry_id) {
                // يمكن إضافة منطق إلغاء القيد المحاسبي هنا
                Log::info('يجب إلغاء القيد المحاسبي', [
                    'journal_entry_id' => $stockDoc->journal_entry_id
                ]);
            }

            DB::commit();

            Log::info('تم إلغاء المستند المخزني بنجاح', [
                'stock_doc_id' => $stockDoc->id
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('فشل في إلغاء المستند المخزني', [
                'stock_doc_id' => $stockDoc->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * عكس تحديث رصيد صنف محدد
     */
    private function reverseItemStockBalance(StockDocItem $stockDocItem, StockDoc $stockDoc): void
    {
        $item = $stockDocItem->item;
        $location = $stockDoc->location;
        
        $stock = Stock::where('item_id', $item->id)
                     ->where('location_id', $location->id)
                     ->first();

        if (!$stock) {
            throw new \Exception("لم يتم العثور على رصيد للصنف '{$item->name}' في الموقع '{$location->name}'");
        }

        // عكس التغيير في الكمية
        $quantityChange = -$this->calculateQuantityChange($stockDocItem, $stockDoc);
        $newQuantity = $stock->quantity + $quantityChange;

        if ($newQuantity < 0 && !$this->allowNegativeStock()) {
            throw new \Exception("لا يمكن إلغاء المستند. الرصيد المتاح للصنف '{$item->name}' غير كافي.");
        }

        $stock->update(['quantity' => $newQuantity]);

        Log::info('تم عكس تحديث رصيد المخزون', [
            'item_id' => $item->id,
            'location_id' => $location->id,
            'quantity_change' => $quantityChange,
            'new_quantity' => $newQuantity
        ]);
    }
}
