<?php

namespace App\Models;
use Carbon\Carbon;

use IFRS\Models\Entity as IFEntity;
use App\Traits\HasCommissionTrait;
use Illuminate\Support\Facades\Auth;
use App\Models\Scopes\BranchEntityScope;
use Spatie\Permission\Traits\HasRoles;
use IFRS\Exceptions\MissingReportingCurrency;
use App\Traits\Owner\OwnerIdTrait;
class Entity extends IFEntity
{
    protected $table = 'ifrs_entities';
    use HasRoles;
    use HasCommissionTrait;
    use OwnerIdTrait;
    public function getReportingCurrencyAttribute(): Currency
    {
       // dd($this->currency);
        if (is_null($this->currency) && is_null($this->parent)) {
            throw new MissingReportingCurrency($this->name);
        }

        return is_null($this->parent) ? $this->currency : $this->parent->currency;
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function parent()
    {
        return $this->belongsTo(Entity::class);
    }

    public function siblings()
    {
        return $this->hasMany(self::class, 'company_id', 'company_id')
            //->where('id', '!=', $this->id)
            ;
    }

    /**
     * Get the IDs of all sibling entities using an accessor.
     *
     * @return array
     */
    public function getSiblingIdsAttribute(): array
    {
        return $this->siblings()->pluck('id')->toArray();
    }
    // protected $guard_name = 'web'; // or whatever guard you want to use


      public function daughters()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    /**
     * Get the IDs of all daughter entities if the entity is a parent,
     * or all sibling entities if the entity is a child.
     *
     * @return array
     */
    public function getBranchIdsAttribute(): array
    {
        if (is_null($this->parent_id)) {
            // This is a parent entity, get its daughters (branches).
            return $this->daughters()->pluck('id')->toArray();
        }

        // This is a child entity (branch), get its siblings.
        return $this->siblings()->pluck('id')->toArray();
    }
    // protected $guard_name = 'web'; // or whatever guard you want to use

    public function getDefaultRateAttribute(): ExchangeRate
    {

        $now = Carbon::now();
        $existing = ExchangeRate::where([
            "company_id" => $this->company_id,
            "currency_id" => $this->currency_id,
        ])->where("valid_from", "<=", $now)
            ->first();
// dd($this->company_id,$this->currency_id, $existing);
        if (!is_null($existing)) {
            return $existing;
        }

        // إنشاء سعر صرف افتراضي إذا لم يوجد
        $new = new ExchangeRate([
            'valid_from' => $now,
            'currency_id' => $this->currency_id,
            "rate" => 1,
            'entity_id' => $this->id
        ]);

        $new->save();

        return $new;
    }


}


