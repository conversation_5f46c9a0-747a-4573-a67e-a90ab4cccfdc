<?php

namespace Database\Factories\Stocks;

use App\Models\Stocks\Stock;
use App\Models\Items\Item;
use App\Models\Stocks\StockLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Stocks\Stock>
 */
class StockFactory extends Factory
{
    protected $model = Stock::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'item_id' => Item::factory(),
            'location_id' => StockLocation::factory(),
            'quantity' => $this->faker->numberBetween(1, 1000),
            'entity_id' => 1, // افتراضي
            'user_id' => 1, // افتراضي
        ];
    }

    /**
     * رصيد فارغ
     */
    public function empty(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => 0,
        ]);
    }

    /**
     * رصيد منخفض
     */
    public function low(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => $this->faker->numberBetween(1, 10),
        ]);
    }

    /**
     * رصيد عالي
     */
    public function high(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => $this->faker->numberBetween(500, 2000),
        ]);
    }

    /**
     * رصيد متوسط
     */
    public function medium(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => $this->faker->numberBetween(50, 200),
        ]);
    }
}
