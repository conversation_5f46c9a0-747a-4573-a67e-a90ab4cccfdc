<?php

namespace App\Policies;


use App\Models\Items\ItemCategory;
use App\Models\User;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Traits\SystemPolicyTraitBefore;

class ItemCategoryPolicy
{
    const USER_TYPE=1;
    use HandlesAuthorization;
    use SystemPolicyTraitBefore;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_item_category');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ItemCategory $itemCategory): bool
    {
        return $user->can('view_item_category');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {  if ($user->entity_id != $user->company_id )
        return $user->can('create_item_category');
        return false;

    }

    // /**
    //  * Determine whether the user can update the model.
    //  */
    // public function update(User $user, Item $item): bool
    // {
    //     return $user->can('update_item');
    // }

    // /**
    //  * Determine whether the user can delete the model.
    //  */
    // public function delete(User $user, Item $item): bool
    // {
    //     return $user->can('delete_item');
    // }

    // /**
    //  * Determine whether the user can bulk delete.
    //  */
    // public function deleteAny(User $user): bool
    // {
    //     return $user->can('delete_any_item');
    // }

    // /**
    //  * Determine whether the user can permanently delete.
    //  */
    // public function forceDelete(User $user, Item $item): bool
    // {
    //     return $user->can('force_delete_item');
    // }

    // /**
    //  * Determine whether the user can permanently bulk delete.
    //  */
    // public function forceDeleteAny(User $user): bool
    // {
    //     return $user->can('{{ ForceDeleteAny }}');
    // }

    // /**
    //  * Determine whether the user can restore.
    //  */
    // public function restore(User $user, ItemCategory $itemCategory): bool
    // {
    //     return $user->can('restore_item_category');
    // }

    // /**
    //  * Determine whether the user can bulk restore.
    //  */
    // public function restoreAny(User $user): bool
    // {
    //     return $user->can('{{ RestoreAny }}');
    // }

    // /**
    //  * Determine whether the user can replicate.
    //  */
    // public function replicate(User $user, ItemCategory $itemCategory): bool
    // {
    //     return $user->can('replicate_item_category');
    // }

    // /**
    //  * Determine whether the user can reorder.
    //  */
    // public function reorder(User $user): bool
    // {
    //     return $user->can('{{ Reorder }}');
    // }
}
