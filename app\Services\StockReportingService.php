<?php

namespace App\Services;

use App\Models\Stocks\StockDoc;
use App\Models\Stocks\Stock;
use App\Models\Items\Item;
use App\Models\Stocks\StockLocation;
use App\Models\JournalEntry;
use App\Settings\StockAccountingSettings;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * خدمة تقارير المخزون المحاسبية
 */
class StockReportingService
{
    private StockAccountingSettings $settings;

    public function __construct()
    {
        $this->settings = app(StockAccountingSettings::class);
    }

    /**
     * الحصول على تقرير قيمة المخزون (alias)
     */
    public function getInventoryValuation(array $filters = []): array
    {
        $locationId = $filters['location_id'] ?? null;
        $asOfDate = isset($filters['as_of_date']) ? Carbon::parse($filters['as_of_date']) : null;

        return $this->getInventoryValuationReport($locationId, $asOfDate);
    }

    /**
     * الحصول على حركات المخزون (alias)
     */
    public function getStockMovements(array $filters = []): array
    {
        $startDate = isset($filters['start_date']) ? Carbon::parse($filters['start_date']) : Carbon::now()->startOfMonth();
        $endDate = isset($filters['end_date']) ? Carbon::parse($filters['end_date']) : Carbon::now()->endOfMonth();
        $locationId = $filters['location_id'] ?? null;
        $itemId = $filters['item_id'] ?? null;

        return $this->getStockMovementReport($startDate, $endDate, $locationId, $itemId);
    }

    /**
     * الحصول على الأصناف بطيئة الحركة (alias)
     */
    public function getSlowMovingItems(int $days = 90): array
    {
        return $this->getSlowMovingItemsReport($days);
    }

    /**
     * تقرير قيمة المخزون
     */
    public function getInventoryValuationReport(?int $locationId = null, ?Carbon $asOfDate = null): array
    {
        $query = Stock::with(['item', 'location'])
                     ->where('quantity', '>', 0);

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $stocks = $query->get();

        $report = [
            'as_of_date' => $asOfDate ? $asOfDate->format('Y-m-d') : Carbon::now()->format('Y-m-d'),
            'total_items' => $stocks->count(),
            'total_quantity' => $stocks->sum('quantity'),
            'total_value' => 0,
            'locations' => [],
            'items' => []
        ];

        $locationGroups = $stocks->groupBy('location_id');

        foreach ($locationGroups as $locationId => $locationStocks) {
            $location = $locationStocks->first()->location;
            $locationValue = 0;

            $locationItems = [];
            foreach ($locationStocks as $stock) {
                $itemValue = $stock->quantity * $stock->item->cost;
                $locationValue += $itemValue;

                $locationItems[] = [
                    'item_id' => $stock->item->id,
                    'item_name' => $stock->item->name,
                    'item_sku' => $stock->item->sku_code,
                    'quantity' => $stock->quantity,
                    'unit_cost' => $stock->item->cost,
                    'total_value' => $itemValue,
                    'valuation_method' => $stock->item->getValuationMethod()
                ];
            }

            $report['locations'][] = [
                'location_id' => $location->id,
                'location_name' => $location->name,
                'items_count' => count($locationItems),
                'total_quantity' => $locationStocks->sum('quantity'),
                'total_value' => $locationValue,
                'items' => $locationItems
            ];

            $report['total_value'] += $locationValue;
        }

        return $report;
    }

    /**
     * تقرير حركات المخزون
     */
    public function getStockMovementReport(
        ?int $itemId = null,
        ?int $locationId = null,
        ?Carbon $fromDate = null,
        ?Carbon $toDate = null
    ): array {
        $query = StockDoc::with(['items.item', 'location', 'journalEntry'])
                         ->orderBy('transaction_date', 'desc');

        if ($fromDate) {
            $query->where('transaction_date', '>=', $fromDate);
        }

        if ($toDate) {
            $query->where('transaction_date', '<=', $toDate);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $stockDocs = $query->get();

        // تصفية بناءً على الصنف إذا تم تحديده
        if ($itemId) {
            $stockDocs = $stockDocs->filter(function ($stockDoc) use ($itemId) {
                return $stockDoc->items->contains('item_id', $itemId);
            });
        }

        $movements = [];
        foreach ($stockDocs as $stockDoc) {
            foreach ($stockDoc->items as $stockDocItem) {
                if (!$itemId || $stockDocItem->item_id == $itemId) {
                    $movements[] = [
                        'date' => $stockDoc->transaction_date,
                        'document_id' => $stockDoc->id,
                        'transaction_type' => $stockDoc->transaction_type,
                        'location_name' => $stockDoc->location->name,
                        'item_name' => $stockDocItem->item->name,
                        'item_sku' => $stockDocItem->item->sku_code,
                        'quantity' => $stockDocItem->quantity,
                        'unit_cost' => $stockDocItem->cost,
                        'total_cost' => $stockDocItem->quantity * $stockDocItem->cost,
                        'unit_price' => $stockDocItem->price,
                        'total_price' => $stockDocItem->quantity * ($stockDocItem->price ?? 0),
                        'journal_entry_id' => $stockDoc->journal_entry_id,
                        'accounting_posted' => $stockDoc->accounting_posted
                    ];
                }
            }
        }

        return [
            'period' => [
                'from' => $fromDate ? $fromDate->format('Y-m-d') : null,
                'to' => $toDate ? $toDate->format('Y-m-d') : null
            ],
            'filters' => [
                'item_id' => $itemId,
                'location_id' => $locationId
            ],
            'total_movements' => count($movements),
            'movements' => $movements
        ];
    }

    /**
     * تقرير القيود المحاسبية للمخزون
     */
    public function getStockAccountingReport(
        ?Carbon $fromDate = null,
        ?Carbon $toDate = null
    ): array {
        $query = StockDoc::with(['journalEntry.lineItems.account', 'location'])
                         ->whereNotNull('journal_entry_id')
                         ->orderBy('transaction_date', 'desc');

        if ($fromDate) {
            $query->where('transaction_date', '>=', $fromDate);
        }

        if ($toDate) {
            $query->where('transaction_date', '<=', $toDate);
        }

        $stockDocs = $query->get();

        $entries = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($stockDocs as $stockDoc) {
            if ($stockDoc->journalEntry) {
                $entryDebits = 0;
                $entryCredits = 0;
                $lineItems = [];

                foreach ($stockDoc->journalEntry->lineItems as $lineItem) {
                    $amount = $lineItem->amount;
                    
                    if ($lineItem->credited) {
                        $entryCredits += $amount;
                        $totalCredits += $amount;
                    } else {
                        $entryDebits += $amount;
                        $totalDebits += $amount;
                    }

                    $lineItems[] = [
                        'account_id' => $lineItem->account_id,
                        'account_name' => $lineItem->account->name ?? 'غير محدد',
                        'account_code' => $lineItem->account->code ?? '',
                        'narration' => $lineItem->narration,
                        'debit' => $lineItem->credited ? 0 : $amount,
                        'credit' => $lineItem->credited ? $amount : 0
                    ];
                }

                $entries[] = [
                    'stock_doc_id' => $stockDoc->id,
                    'journal_entry_id' => $stockDoc->journal_entry_id,
                    'date' => $stockDoc->transaction_date,
                    'transaction_type' => $stockDoc->transaction_type,
                    'location_name' => $stockDoc->location->name,
                    'reference' => $stockDoc->journalEntry->reference,
                    'narration' => $stockDoc->journalEntry->narration,
                    'total_debits' => $entryDebits,
                    'total_credits' => $entryCredits,
                    'line_items' => $lineItems
                ];
            }
        }

        return [
            'period' => [
                'from' => $fromDate ? $fromDate->format('Y-m-d') : null,
                'to' => $toDate ? $toDate->format('Y-m-d') : null
            ],
            'summary' => [
                'total_entries' => count($entries),
                'total_debits' => $totalDebits,
                'total_credits' => $totalCredits,
                'balance_check' => abs($totalDebits - $totalCredits) < 0.01
            ],
            'entries' => $entries
        ];
    }

    /**
     * تقرير تحليل ربحية الأصناف
     */
    public function getItemProfitabilityReport(
        ?Carbon $fromDate = null,
        ?Carbon $toDate = null
    ): array {
        // البحث عن مستندات التسليم (المبيعات)
        $query = StockDoc::with(['items.item'])
                         ->where('transaction_type', StockDoc::DELIVERY);

        if ($fromDate) {
            $query->where('transaction_date', '>=', $fromDate);
        }

        if ($toDate) {
            $query->where('transaction_date', '<=', $toDate);
        }

        $salesDocs = $query->get();

        $itemProfits = [];

        foreach ($salesDocs as $salesDoc) {
            foreach ($salesDoc->items as $stockDocItem) {
                $itemId = $stockDocItem->item_id;
                $item = $stockDocItem->item;

                if (!isset($itemProfits[$itemId])) {
                    $itemProfits[$itemId] = [
                        'item_id' => $itemId,
                        'item_name' => $item->name,
                        'item_sku' => $item->sku_code,
                        'total_quantity_sold' => 0,
                        'total_sales_value' => 0,
                        'total_cost_value' => 0,
                        'total_profit' => 0,
                        'profit_margin' => 0
                    ];
                }

                $quantity = $stockDocItem->quantity;
                $salesValue = $quantity * ($stockDocItem->price ?? 0);
                $costValue = $quantity * $item->cost;
                $profit = $salesValue - $costValue;

                $itemProfits[$itemId]['total_quantity_sold'] += $quantity;
                $itemProfits[$itemId]['total_sales_value'] += $salesValue;
                $itemProfits[$itemId]['total_cost_value'] += $costValue;
                $itemProfits[$itemId]['total_profit'] += $profit;
            }
        }

        // حساب هامش الربح
        foreach ($itemProfits as &$itemProfit) {
            if ($itemProfit['total_sales_value'] > 0) {
                $itemProfit['profit_margin'] = ($itemProfit['total_profit'] / $itemProfit['total_sales_value']) * 100;
            }
        }

        // ترتيب حسب الربح
        $itemProfits = collect($itemProfits)->sortByDesc('total_profit')->values()->toArray();

        $totalSales = array_sum(array_column($itemProfits, 'total_sales_value'));
        $totalCosts = array_sum(array_column($itemProfits, 'total_cost_value'));
        $totalProfit = $totalSales - $totalCosts;

        return [
            'period' => [
                'from' => $fromDate ? $fromDate->format('Y-m-d') : null,
                'to' => $toDate ? $toDate->format('Y-m-d') : null
            ],
            'summary' => [
                'total_items' => count($itemProfits),
                'total_sales_value' => $totalSales,
                'total_cost_value' => $totalCosts,
                'total_profit' => $totalProfit,
                'overall_profit_margin' => $totalSales > 0 ? ($totalProfit / $totalSales) * 100 : 0
            ],
            'items' => $itemProfits
        ];
    }

    /**
     * تقرير الأصناف بطيئة الحركة
     */
    public function getSlowMovingItemsReport(int $daysThreshold = 90): array
    {
        $thresholdDate = Carbon::now()->subDays($daysThreshold);

        // البحث عن الأصناف التي لم تتحرك منذ فترة
        $slowMovingItems = Stock::with(['item', 'location'])
                                ->where('quantity', '>', 0)
                                ->whereDoesntHave('item.stockDocItems', function ($query) use ($thresholdDate) {
                                    $query->whereHas('stockDoc', function ($subQuery) use ($thresholdDate) {
                                        $subQuery->where('transaction_date', '>=', $thresholdDate)
                                                ->where('transaction_type', StockDoc::DELIVERY);
                                    });
                                })
                                ->get();

        $items = [];
        $totalValue = 0;

        foreach ($slowMovingItems as $stock) {
            $itemValue = $stock->quantity * $stock->item->cost;
            $totalValue += $itemValue;

            $items[] = [
                'item_id' => $stock->item->id,
                'item_name' => $stock->item->name,
                'item_sku' => $stock->item->sku_code,
                'location_name' => $stock->location->name,
                'quantity' => $stock->quantity,
                'unit_cost' => $stock->item->cost,
                'total_value' => $itemValue,
                'days_since_last_sale' => $this->getDaysSinceLastSale($stock->item->id)
            ];
        }

        return [
            'threshold_days' => $daysThreshold,
            'threshold_date' => $thresholdDate->format('Y-m-d'),
            'total_items' => count($items),
            'total_value_at_risk' => $totalValue,
            'items' => $items
        ];
    }

    /**
     * حساب عدد الأيام منذ آخر بيع للصنف
     */
    private function getDaysSinceLastSale(int $itemId): ?int
    {
        $lastSale = StockDoc::whereHas('items', function ($query) use ($itemId) {
                              $query->where('item_id', $itemId);
                          })
                          ->where('transaction_type', StockDoc::DELIVERY)
                          ->orderBy('transaction_date', 'desc')
                          ->first();

        if ($lastSale) {
            return Carbon::parse($lastSale->transaction_date)->diffInDays(Carbon::now());
        }

        return null; // لم يتم بيع الصنف من قبل
    }
}
