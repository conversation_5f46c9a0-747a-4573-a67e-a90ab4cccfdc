<?php

namespace App\Policies;


use App\Models\Stocks\DeliveryStock;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use App\Traits\SystemPolicyTraitBefore;
// use App\Traits\Policy\DisableCreateOperationTrait;
class DeliveryStockPolicy extends BasePolicy
{
    const USER_TYPE=1;
    use HandlesAuthorization;
    use SystemPolicyTraitBefore;
    // use DisableCreateOperationTrait;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_delivery::stock');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DeliveryStock $deliveryStock): bool
    {
        return $user->can('view_delivery::stock');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        if ($user->entity_id != $user->company_id )
        return $user->can('create_delivery::stock');
    return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    // public function update(User $user, OpeningStock $openingStock): bool
    // {
    //     return $user->can('update_opening::stock');
    // }

    // /**
    //  * Determine whether the user can delete the model.
    //  */
    // public function delete(User $user, OpeningStock $openingStock): bool
    // {
    //     return $user->can('delete_opening::stock');
    // }

    // /**
    //  * Determine whether the user can bulk delete.
    //  */
    // public function deleteAny(User $user): bool
    // {
    //     return $user->can('delete_any_opening::stock');
    // }

    // /**
    //  * Determine whether the user can permanently delete.
    //  */
    // public function forceDelete(User $user, OpeningStock $openingStock): bool
    // {
    //     return $user->can('force_delete_opening::stock');
    // }

    // /**
    //  * Determine whether the user can permanently bulk delete.
    //  */
    // public function forceDeleteAny(User $user): bool
    // {
    //     return $user->can('{{ ForceDeleteAny }}');
    // }

    // /**
    //  * Determine whether the user can restore.
    //  */
    // public function restore(User $user, OpeningStock $openingStock): bool
    // {
    //     return $user->can('restore_opening::stock');
    // }

    // /**
    //  * Determine whether the user can bulk restore.
    //  */
    // public function restoreAny(User $user): bool
    // {
    //     return $user->can('{{ RestoreAny }}');
    // }

    // /**
    //  * Determine whether the user can replicate.
    //  */
    // public function replicate(User $user, OpeningStock $openingStock): bool
    // {
    //     return $user->can('{{ Replicate }}');
    // }

    // /**
    //  * Determine whether the user can reorder.
    //  */
    // public function reorder(User $user): bool
    // {
    //     return $user->can('{{ Reorder }}');
    // }
}
