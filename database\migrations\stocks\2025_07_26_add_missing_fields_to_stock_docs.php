<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('inv_stock_docs', function (Blueprint $table) {
            if (!Schema::hasColumn('inv_stock_docs', 'total_amount')) {
                $table->decimal('total_amount', 15, 2)->default(0)->after('note');
            }

            if (!Schema::hasColumn('inv_stock_docs', 'status')) {
                $table->enum('status', ['draft', 'confirmed', 'cancelled'])->default('draft')->after('total_amount');
            }
        });
    }

    public function down()
    {
        Schema::table('inv_stock_docs', function (Blueprint $table) {
            $table->dropColumn(['total_amount', 'status']);
        });
    }
};
