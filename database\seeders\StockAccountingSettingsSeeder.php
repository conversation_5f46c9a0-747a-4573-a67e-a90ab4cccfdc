<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StockAccountingSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            'default_inventory_account' => null,
            'inventory_adjustment_account' => null,
            'cost_of_goods_sold_account' => null,
            'sales_account' => null,
            'purchases_account' => null,
            'opening_balance_account' => null,
            'retained_earnings_account' => null,
            'cash_account' => null,
            'bank_account' => null,
            'default_customer_account' => null,
            'default_supplier_account' => null,
            'accounts_receivable_account' => null,
            'accounts_payable_account' => null,
            'auto_create_journal_entries' => true,
            'create_cogs_entries' => true,
            'separate_location_accounts' => false,
            'inventory_valuation_method' => 'fifo',
            'use_standard_cost' => false,
            'input_tax_account' => null,
            'output_tax_account' => null,
            'include_tax_in_inventory_cost' => true
        ];

        // إنشاء إعدادات للشركة الافتراضية
        DB::table('settings')->updateOrInsert(
            [
                'group' => 'stock_accounting_1',
                'name' => 'stock_accounting_settings'
            ],
            [
                'locked' => false,
                'payload' => json_encode($settings),
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        $this->command->info('Stock accounting settings seeded successfully!');
    }
}
