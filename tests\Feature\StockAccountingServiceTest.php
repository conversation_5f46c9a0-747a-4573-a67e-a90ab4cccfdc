<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\StockAccountingService;
use App\Models\Stocks\StockDoc;
use App\Models\Stocks\StockDocItem;
use App\Models\Stocks\StockLocation;
use App\Models\Items\Item;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Settings\StockAccountingSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class StockAccountingServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private StockAccountingService $service;
    private StockAccountingSettings $settings;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(StockAccountingService::class);
        $this->settings = app(StockAccountingSettings::class);
        
        // إعداد الحسابات المطلوبة للاختبار
        $this->setupTestAccounts();
    }

    private function setupTestAccounts(): void
    {
        // إنشاء حسابات اختبارية
        $inventoryAccount = Account::factory()->create([
            'name' => 'حساب المخزون',
            'account_type' => \IFRS\Models\Account::INVENTORY,
        ]);

        $salesAccount = Account::factory()->create([
            'name' => 'حساب المبيعات',
            'account_type' => \IFRS\Models\Account::OPERATING_REVENUE,
        ]);

        $cogsAccount = Account::factory()->create([
            'name' => 'تكلفة البضاعة المباعة',
            'account_type' => \IFRS\Models\Account::OPERATING_EXPENSE,
        ]);

        $cashAccount = Account::factory()->create([
            'name' => 'النقدية',
            'account_type' => \IFRS\Models\Account::BANK,
        ]);

        // تحديث الإعدادات
        $this->settings->default_inventory_account = $inventoryAccount->id;
        $this->settings->sales_account = $salesAccount->id;
        $this->settings->cost_of_goods_sold_account = $cogsAccount->id;
        $this->settings->cash_account = $cashAccount->id;
        $this->settings->auto_create_journal_entries = true;
        $this->settings->save();
    }

    /** @test */
    public function it_creates_journal_entry_for_opening_stock(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create(['cost' => 100]);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 10,
            'cost' => 100,
        ]);

        // تنفيذ الاختبار
        $journalEntry = $this->service->createStockJournalEntry($stockDoc);

        // التحقق من النتائج
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertEquals(2, $journalEntry->lineItems->count());
        
        // التحقق من توازن القيد
        $debits = $journalEntry->lineItems->where('credited', false)->sum('amount');
        $credits = $journalEntry->lineItems->where('credited', true)->sum('amount');
        $this->assertEquals($debits, $credits);
        $this->assertEquals(1000, $debits); // 10 * 100
    }

    /** @test */
    public function it_creates_journal_entry_for_receipt_stock(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create(['cost' => 50]);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::RECEIPT,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 20,
            'cost' => 50,
        ]);

        // تنفيذ الاختبار
        $journalEntry = $this->service->createStockJournalEntry($stockDoc);

        // التحقق من النتائج
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertEquals(2, $journalEntry->lineItems->count());
        
        // التحقق من الحسابات المستخدمة
        $inventoryLineItem = $journalEntry->lineItems->where('credited', false)->first();
        $this->assertEquals($this->settings->default_inventory_account, $inventoryLineItem->account_id);
        $this->assertEquals(1000, $inventoryLineItem->amount); // 20 * 50
    }

    /** @test */
    public function it_creates_journal_entry_for_delivery_stock(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create(['cost' => 30, 'price' => 50]);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::DELIVERY,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 5,
            'cost' => 30,
            'price' => 50,
        ]);

        // تنفيذ الاختبار
        $journalEntry = $this->service->createStockJournalEntry($stockDoc);

        // التحقق من النتائج
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        
        // التحقق من قيد المبيعات
        $salesLineItem = $journalEntry->lineItems->where('credited', true)->first();
        $this->assertEquals(250, $salesLineItem->amount); // 5 * 50

        // التحقق من إنشاء قيد تكلفة البضاعة المباعة
        $cogsEntries = JournalEntry::where('narration', 'like', '%تكلفة البضاعة المباعة%')->get();
        $this->assertGreaterThan(0, $cogsEntries->count());
    }

    /** @test */
    public function it_creates_journal_entry_for_adjustment_stock(): void
    {
        // إعداد البيانات
        $location = StockLocation::factory()->create();
        $item = Item::factory()->create(['cost' => 25]);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::ADJUSTMENT,
            'location_id' => $location->id,
        ]);

        // تسوية بالزيادة
        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 8,
            'cost' => 25,
        ]);

        // تنفيذ الاختبار
        $journalEntry = $this->service->createStockJournalEntry($stockDoc);

        // التحقق من النتائج
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertEquals(2, $journalEntry->lineItems->count());
        
        // التحقق من مبلغ التسوية
        $adjustmentAmount = $journalEntry->lineItems->sum('amount') / 2; // نفس المبلغ مدين ودائن
        $this->assertEquals(200, $adjustmentAmount); // 8 * 25
    }

    /** @test */
    public function it_throws_exception_when_required_accounts_missing(): void
    {
        // إزالة الحسابات المطلوبة
        $this->settings->default_inventory_account = null;
        $this->settings->save();

        $location = StockLocation::factory()->create();
        $item = Item::factory()->create();
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 1,
            'cost' => 100,
        ]);

        // توقع استثناء
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('لم يتم تحديد حساب المخزون الافتراضي في الإعدادات');

        $this->service->createStockJournalEntry($stockDoc);
    }

    /** @test */
    public function it_uses_item_specific_accounts_when_available(): void
    {
        // إنشاء حساب خاص بالصنف
        $itemInventoryAccount = Account::factory()->create([
            'name' => 'حساب مخزون خاص',
            'account_type' => \IFRS\Models\Account::INVENTORY,
        ]);

        $location = StockLocation::factory()->create();
        $item = Item::factory()->create([
            'cost' => 75,
            'inventory_account_id' => $itemInventoryAccount->id,
        ]);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        $stockDocItem = StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item->id,
            'quantity' => 4,
            'cost' => 75,
        ]);

        // تنفيذ الاختبار
        $journalEntry = $this->service->createStockJournalEntry($stockDoc);

        // التحقق من استخدام الحساب الخاص بالصنف
        $inventoryLineItem = $journalEntry->lineItems->where('credited', false)->first();
        $this->assertEquals($itemInventoryAccount->id, $inventoryLineItem->account_id);
    }

    /** @test */
    public function it_handles_multiple_items_in_single_document(): void
    {
        $location = StockLocation::factory()->create();
        $item1 = Item::factory()->create(['cost' => 100]);
        $item2 = Item::factory()->create(['cost' => 200]);
        
        $stockDoc = StockDoc::factory()->create([
            'transaction_type' => StockDoc::OPENING,
            'location_id' => $location->id,
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item1->id,
            'quantity' => 5,
            'cost' => 100,
        ]);

        StockDocItem::factory()->create([
            'stock_doc_id' => $stockDoc->id,
            'item_id' => $item2->id,
            'quantity' => 3,
            'cost' => 200,
        ]);

        // تنفيذ الاختبار
        $journalEntry = $this->service->createStockJournalEntry($stockDoc);

        // التحقق من النتائج
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        
        // يجب أن يكون هناك بند لكل صنف + بند واحد للرصيد الافتتاحي
        $this->assertEquals(3, $journalEntry->lineItems->count());
        
        // التحقق من إجمالي المبلغ
        $totalAmount = (5 * 100) + (3 * 200); // 500 + 600 = 1100
        $debits = $journalEntry->lineItems->where('credited', false)->sum('amount');
        $credits = $journalEntry->lineItems->where('credited', true)->sum('amount');
        
        $this->assertEquals($totalAmount, $debits);
        $this->assertEquals($totalAmount, $credits);
    }
}
