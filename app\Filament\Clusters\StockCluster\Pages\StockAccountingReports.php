<?php

namespace App\Filament\Clusters\StockCluster\Pages;

use App\Filament\Clusters\StockCluster;
use App\Services\StockReportingService;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;

class StockAccountingReports extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static string $view = 'filament.clusters.stock-cluster.pages.stock-accounting-reports';

    protected static ?string $cluster = StockCluster::class;

    protected static ?int $navigationSort = 20;

    public static function getNavigationLabel(): string
    {
        return 'التقارير المحاسبية للمخازن';
    }

    public function getTitle(): string
    {
        return 'التقارير المحاسبية للمخازن';
    }

    // Form data properties
    public $locationId = null;
    public $asOfDate = null;
    public $itemId = null;
    public $fromDate = null;
    public $toDate = null;
    public $daysThreshold = 90;
    public $activeTab = 0;

    private StockReportingService $reportingService;

    public function mount(): void
    {
        $this->reportingService = app(StockReportingService::class);
        $this->asOfDate = Carbon::now()->format('Y-m-d');
        $this->fromDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->toDate = Carbon::now()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('reports')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('valuation')
                            ->label('تقرير قيمة المخزون')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Select::make('locationId')
                                            ->label('الموقع')
                                            ->options(\App\Models\Stocks\StockLocation::pluck('name', 'id'))
                                            ->placeholder('جميع المواقع'),

                                        Forms\Components\DatePicker::make('asOfDate')
                                            ->label('كما في تاريخ')
                                            ->default(Carbon::now()),

                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('generate_valuation')
                                                ->label('إنشاء التقرير')
                                                ->action('generateValuationReport')
                                                ->color('primary'),
                                        ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('movements')
                            ->label('تقرير حركات المخزون')
                            ->schema([
                                Forms\Components\Grid::make(4)
                                    ->schema([
                                        Forms\Components\Select::make('itemId')
                                            ->label('الصنف')
                                            ->options(\App\Models\Items\Item::pluck('name', 'id'))
                                            ->placeholder('جميع الأصناف'),

                                        Forms\Components\DatePicker::make('fromDate')
                                            ->label('من تاريخ')
                                            ->default(Carbon::now()->startOfMonth()),

                                        Forms\Components\DatePicker::make('toDate')
                                            ->label('إلى تاريخ')
                                            ->default(Carbon::now()),

                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('generate_movements')
                                                ->label('إنشاء التقرير')
                                                ->action('generateMovementsReport')
                                                ->color('primary'),
                                        ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('accounting')
                            ->label('تقرير القيود المحاسبية')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\DatePicker::make('fromDate')
                                            ->label('من تاريخ')
                                            ->default(Carbon::now()->startOfMonth()),

                                        Forms\Components\DatePicker::make('toDate')
                                            ->label('إلى تاريخ')
                                            ->default(Carbon::now()),

                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('generate_accounting')
                                                ->label('إنشاء التقرير')
                                                ->action('generateAccountingReport')
                                                ->color('primary'),
                                        ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('profitability')
                            ->label('تقرير ربحية الأصناف')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\DatePicker::make('fromDate')
                                            ->label('من تاريخ')
                                            ->default(Carbon::now()->startOfMonth()),

                                        Forms\Components\DatePicker::make('toDate')
                                            ->label('إلى تاريخ')
                                            ->default(Carbon::now()),

                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('generate_profitability')
                                                ->label('إنشاء التقرير')
                                                ->action('generateProfitabilityReport')
                                                ->color('primary'),
                                        ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('slow_moving')
                            ->label('الأصناف بطيئة الحركة')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('daysThreshold')
                                            ->label('عدد الأيام')
                                            ->numeric()
                                            ->default(90)
                                            ->helperText('الأصناف التي لم تتحرك خلال هذا العدد من الأيام'),

                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('generate_slow_moving')
                                                ->label('إنشاء التقرير')
                                                ->action('generateSlowMovingReport')
                                                ->color('primary'),
                                        ]),
                                    ]),
                            ]),
                    ])
                    ->activeTab($this->activeTab)
                    ->columnSpanFull(),
            ]);
    }

    public function generateValuationReport(): void
    {
        $this->activeTab = 0; // تقرير قيمة المخزون
        // سيتم تنفيذ منطق التقرير في الـ view
    }

    public function generateMovementsReport(): void
    {
        $this->activeTab = 1; // تقرير حركات المخزون
        // سيتم تنفيذ منطق التقرير في الـ view
    }

    public function generateAccountingReport(): void
    {
        $this->activeTab = 2; // تقرير القيود المحاسبية
        // سيتم تنفيذ منطق التقرير في الـ view
    }

    public function generateProfitabilityReport(): void
    {
        $this->activeTab = 3; // تقرير ربحية الأصناف
        // سيتم تنفيذ منطق التقرير في الـ view
    }

    public function generateSlowMovingReport(): void
    {
        $this->activeTab = 4; // تقرير الأصناف بطيئة الحركة
        // سيتم تنفيذ منطق التقرير في الـ view
    }

    public function getViewData(): array
    {
        $data = [];

        // تهيئة الخدمة إذا لم تكن مهيأة
        if (!isset($this->reportingService)) {
            $this->reportingService = app(StockReportingService::class);
        }

        switch ($this->activeTab) {
            case 0: // valuation
                $data['valuationReport'] = $this->reportingService->getInventoryValuationReport(
                    $this->locationId,
                    $this->asOfDate ? Carbon::parse($this->asOfDate) : null
                );
                break;

            case 1: // movements
                $data['movementsReport'] = $this->reportingService->getStockMovementReport(
                    $this->itemId,
                    $this->locationId,
                    $this->fromDate ? Carbon::parse($this->fromDate) : null,
                    $this->toDate ? Carbon::parse($this->toDate) : null
                );
                break;

            case 2: // accounting
                $data['accountingReport'] = $this->reportingService->getStockAccountingReport(
                    $this->fromDate ? Carbon::parse($this->fromDate) : null,
                    $this->toDate ? Carbon::parse($this->toDate) : null
                );
                break;

            case 3: // profitability
                $data['profitabilityReport'] = $this->reportingService->getItemProfitabilityReport(
                    $this->fromDate ? Carbon::parse($this->fromDate) : null,
                    $this->toDate ? Carbon::parse($this->toDate) : null
                );
                break;

            case 4: // slow_moving
                $data['slowMovingReport'] = $this->reportingService->getSlowMovingItemsReport(90);
                break;
        }

        return $data;
    }
}
